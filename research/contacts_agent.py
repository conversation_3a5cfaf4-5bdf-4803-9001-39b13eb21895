import asyncio
import logging
from typing import Any, AsyncIterator

import dotenv
from langchain.tools import tool
from langchain_core.messages import AIMessageChunk, BaseMessage, ToolMessage
from langchain_core.prompts import ChatPromptTemplate
from langgraph.prebuilt import create_react_agent

from utils import init_model

logger = logging.getLogger(__name__)


@tool
async def think_people_search_strategy(user_query: str, organization_info: str) -> str:
    """
    Think about the best strategy to search for contacts in a target organization based on the user's requirements.

    Args:
        user_query: The user's original query or requirements for contact search
        organization_info: 目标公司的信息，需要包含公司名称、公司主营业务/行业、公司规模等。至少100字。

    Returns:
        A detailed analysis of search strategy including recommended keywords, seniorities, and other search parameters
    """
    # this is a thinking tool that helps analyze how to search for contacts effectively
    # the implementation will use LLM to think through the best search strategy

    prompt = f"""
你是一个智能且经验丰富的映翰通（InHand Networks）销售助手。你的任务是生成一套最佳的 LinkedIn 联系人搜索策略，以便高效识别出目标公司中适合作为潜在客户的联系人。

**你的目标是：**

根据映翰通的产品特性、销售场景以及对目标公司的分析，制定出最有效率且精准的 LinkedIn 搜索参数（包括职位关键词、职级和部门），明确评估和筛选潜在联系人的核心逻辑，并提供当候选人过多时的精细化过滤策略。这些将共同指导后续的联系人搜索、初步筛选和深入分析，确保我们能找到高价值且易于进行销售跟进的潜在客户。

**请根据以下信息进行分析并生成策略：**

1.  **映翰通产品及解决方案概述：**
    * 工业通信设备：工业级路由器、蜂窝网关、以太网交换机、无线数据终端（DTU）等。
    * 边缘计算平台：边缘计算网关、AI加速计算机。
    * 云管理平台：DeviceLive、InConnect。
    * 行业解决方案：智能配电网、数字化工厂、智能售货系统、车载通信等。
    * 核心价值：助力客户实现数字化转型和智能化升级。

2.  **本次销售场景与目标产品：**
    {user_query}

3.  **目标公司信息：**
    {organization_info}

**请深入思考以下问题，并以此为基础制定你的搜索策略、选人逻辑和过滤策略：**

1.  **映翰通的[特定产品/解决方案]最可能解决目标公司在[公司主营业务/行业]中的哪些具体痛点或需求？** （例如：数据采集不及时、远程设备管理困难、网络安全性不足、生产效率低下、缺乏边缘智能、传统设备无法联网）
2.  **基于这些痛点和映翰通的产品优势，目标公司内哪些职能部门最有可能负责解决这些问题、推动相关项目或进行技术选型？** （例如：IT部门、运营技术(OT)部门、研发部门、数字化转型部门、自动化部门、生产部门、基础设施部门、供应链部门、产品管理部门、创新部门）
3.  **在这些核心部门中，哪些职级的人员最有可能具备采购决策权、技术影响力或项目实施职责？** （例如：总监/Director、经理/Manager、副总裁/VP、主管/Head、首席XX官/CXO、高级工程师/Senior Engineer、架构师/Architect、技术负责人、项目经理）
4.  **根据以上分析，确定最适合作为 LinkedIn 搜索的职位关键词（Job Title Keywords）、职级（Seniority Levels）和部门（Departments）。**
    * **对于大型公司：** 在制定搜索策略时，即使是初始的“宽松”搜索条件，也应从这些最相关的部门和中高级职级开始，避免使用过于宽泛或不限制任何条件的搜索，以减少无关结果。策略应倾向于“策略性地宽泛”（strategically broad）而非“无限制”（unrestricted）。
    * **职位关键词考量：** 务必考虑与映翰通产品相关的技术词汇、行业通用职位名称及其同义词或相关变体。（例如：物联网、IIoT、工业4.0、边缘计算、数字化转型、智能制造、工业自动化、SCADA、OT网络、数据分析、IT经理、运营总监等）。
5.  **潜在联系人评估标准（选人逻辑）：**
    * **高价值潜在客户的关键特征：** 哪些特征（例如：职位描述中包含的项目经验、负责的业务范围、技能列表、过往成就、LinkedIn活跃度等）会使一个联系人成为高潜力的映翰通潜在客户？请具体说明与映翰通产品直接相关的关键要素。
        * **职责范围：** 是否负责或影响与物联网、工业网络、自动化、IT/OT融合、数字化转型、数据分析或特定行业应用（如智能交通、智能制造）相关的策略或项目。
        * **经验背景：** 是否有成功实施过类似项目或管理过相关技术的经验。
        * **技能：** 是否具备工业通信协议、网络安全、云计算、边缘计算、AI、数据科学等相关技能。
        * **公司内影响力：** 是否为部门负责人、项目决策者或技术选型者。
    * **低价值或不适宜作为潜在客户的特征：** 哪些特征会降低一个联系人作为潜在客户的价值，即使他们符合初步的搜索条件？（例如：职位过于初级、工作内容与物联网或工业领域不直接相关、纯粹的行政/支持性职位、公司内职责范围非常狭窄且无扩展性、已离职或非目标公司员工等）。
6.  **高数量候选人过滤策略（以销售可联系性为导向）：**
    * 当初步搜索结果数量非常大（例如超过100人甚至更多）时，除了收紧职位、部门和职级条件外，应如何进一步高效筛选，以便销售人员能够更实际地进行联系和客户开发？
    * **请从销售人员实际可操作性和客户开发潜力角度，给出优先级排序和精简候选人的具体策略：**
        * **优先级考量：** 哪些职级与部门的组合，最能代表决策者或核心影响者，且最有可能对映翰通的产品产生采购需求？优先考虑这些组合。
        * **资料完整度与相关性：** 优先选择 LinkedIn 资料完整度高、其工作经历或个人简介中明确提及与映翰通产品应用领域（如物联网、工业自动化、数字化转型项目）相关的项目经验或职责的候选人。模糊或过于通用的描述应降级。
        * **活跃度与互动潜力：** 考虑候选人的 LinkedIn 活跃度（如：近期是否有更新、发布过相关文章、参与过行业讨论）。活跃度高的用户可能更容易被接触或对行业趋势更敏感。
        * **潜在痛点匹配度：** 重点关注那些在职位描述或项目经验中能直接看出其所负责的业务领域存在映翰通产品能解决的痛点（例如，描述中提到“优化传统网络”、“实现设备远程监测”、“数据孤岛”等）的联系人。
        * **避免重复或冗余：** 识别并去除明显重复的、已离职的或与目标公司关联度不强的候选人。

# 注意事项
- 根据公司规模，制定不同的选人策略；
- 你要考虑后续的销售客户开发工作如何开发，适合开发什么样的角色；
- 我们的目标主要是卖硬件产品，不是将一个新方案卖给客户，所以你不需要考虑方案的适配性，只需要考虑客户是否需要我们的产品。
```

    """  # noqa: E501

    from langchain_core.output_parsers import StrOutputParser

    llm = init_model(
        model="o4-mini",
        max_tokens=2048,
    )
    chain = llm | StrOutputParser()
    return await chain.ainvoke(prompt)


class ContactsAgent:
    """
    联系人搜索代理
    """

    def __init__(
        self,
        model_name: str = "gpt-4.1",
    ):
        # initialize llm based on model_name or use provided llm
        self.model_name = model_name
        self.llm = init_model(model_name)

        # log important initialization information
        logging.info(f"Initializing ContactsAgent with model: {model_name}")
        self._init_agent()

    def _get_system_prompt(self):
        return """
你是一个智能且具有丰富经验的映翰通（InHand Networks）的销售助手。
你的目标是结合工具，根据用户提供的要求，识别出目标公司中适合作为潜在客户的联系人。
以便我们可以把我的某一个产品卖给目标公司。
你需要从LinkedIn信息中识别合适的联系人，并分析他们作为潜在客户的价值。

## 映翰通
映翰通是一家专注于工业物联网（IIoT）通信与边缘计算的高科技企业。公司致力于为工业、能源、交通、零售等行业提供"云+端"一体化的物联网解决方案，助力客户实现数字化转型和智能化升级。
映翰通的主要产品和服务包括：
- 工业通信设备：如工业级路由器、蜂窝网关、以太网交换机、无线数据终端（DTU）等，广泛应用于工业自动化、能源管理等领域。
- 边缘计算平台：提供边缘计算网关和AI加速计算机，支持本地数据处理和智能分析，提升系统响应速度和安全性。
- 云管理平台：如DeviceLive和InConnect，支持设备远程管理、数据可视化和智能运维。
- 行业解决方案：涵盖智能配电网、数字化工厂、智能售货系统、车载通信等多个领域，提供定制化的物联网应用方案。

## 工作流程
1. 理解用户需求: 分析销售场景，明确目标联系人特征
2. 客户信息挖掘: 使用 `tavily_search`、`search_organization` 工具，分析目标公司的产品、技术、团队和项目公司背景、行业、规模等
3. 制定搜索策略: 使用 think_people_search_strategy 工具分析目标公司，确定最佳搜索参数
   - 根据分析结果确定职位关键词、职级、部门等搜索条件
4. 搜索联系人: 使用搜索工具获取潜在联系人基本信息
    - 先使用必要的、更宽松的搜索条件，(如：不限制职位关键词，不限制职级，不限制部门)；然后再逐渐增加搜索条件直到获取适量候选人
    - 收紧参数时，优先使用步骤3生成的搜索策略中的关键参数
    - 分析返回的pagination信息，根据总结果数动态调整搜索策略:
      - 结果<50人: 按优先级放宽搜索条件(先降低职级要求，再扩大部门范围，最后放宽职位关键词)
      - 结果>100人: 按优先级收紧搜索条件(先提高职级要求，再缩小部门范围，最后细化职位关键词)
      - 结果50-100人: 接受此范围
    - 调整条件直到获取适量候选人，最多输出100人
    - 调整条件，甚至不限制某些条件，让输出人数必须接近100人
    - 当结果超过100人时，必须继续收紧条件，不要直接跳到下一步
    - 当有多页结果时，必须获取所有页面的数据进行综合评估
    - 对小于100人的公司，可考虑无限制条件
5. 初步筛选: 评估搜索结果，筛选最有潜力的15-50位联系人
6. 获取 linkedin 详情: 仅为高潜力联系人获取详细资料，节约API资源
7. 深入分析: 基于 linkedin 详情(工作经历、技能、简介)评估价值
    - 参考资料更新频率、内容丰富度及互动记录判断活跃度
    - 无更新或内容空白者降低推荐信心
    - 确认候选人当前就职于目标公司
8. 最终推荐: 按优先级排序推荐3-10名高价值联系人
    - 结果过少时适度放宽条件并说明原因

## 使用工具
- 请使用think工具进行多步骤思考, 确保思考过程清晰可追踪。
- 请使用tavily_search工具进行搜索，确保搜索结果准确。搜索参数必须使用英文。
- get_linkedin_profiles 工具的 explaination 必须包含你为什么选这些人进行筛选的理由。

## 输出格式
输出合适的联系人，按推荐优先级排序。使用 yaml 格式，字段包含：
- name: 联系人姓名
- position: 联系人职位
- company: 所属公司
- country: 联系人国籍
- profile_url: 联系人LinkedIn个人资料URL
- reason: 推荐理由，必须指出与目标销售场景直接相关的工作内容以及候选人过往相关成就/项目经验。如果有可以包含可能潜在反对的理由。
- confidence: 推荐信心（非常低/低/中等/高/非常高）

## 注意事项
- 综合考虑其职位、工作经历、技能和个人简介
- 使用中文回复用户
- 你必须严格按照[工作流程]进行，不得跳过任何步骤。
- 你的信息收集必须依赖于工具调用，无论用户是否已经提供了信息。
- 工作流程可以反复执行多轮步骤，确保你拿到了足够多的高价值候选人。
- please keep going until the user’s query is completely resolved, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the problem is solved.
"""  # noqa: E501

    def _init_agent(self):
        from agent.tools.apollo import search_organizations, search_people
        from agent.tools.linkedin import get_linkedin_profiles
        from agent.tools.tavily import tavily_search

        tools = [
            get_linkedin_profiles,
            search_organizations,
            search_people,
            # think,
            think_people_search_strategy,
            tavily_search,
        ]
        # tools = [think, think_people_search_strategy]
        # tools += mcp_tools
        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    self._get_system_prompt(),
                ),
                ("placeholder", "{messages}"),
            ]
        )
        self.agent = create_react_agent(self.llm, tools, prompt=prompt)

    def _prepare_input(self, user_query: str, organization_info: str, organization_id: str) -> dict:
        return {
            "messages": [
                f"用户要求：{user_query}",
                f"组织信息：{organization_info}",
                f"组织 ID: {organization_id}",
                "请帮忙找出合适的潜在联系人。请先列出你的完整工作计划，然后按照计划执行。",
            ],
        }

    async def ainvoke(self, user_query: str, organization_info: str, organization_id: str) -> str:
        input = self._prepare_input(user_query, organization_info, organization_id)
        result = await self.agent.ainvoke(
            input=input,
            config={"callbacks": self.callbacks, "recursion_limit": 50},
        )
        return result["messages"][-1].content

    async def astream(
        self, user_query: str, organization_info: str, organization_id: str
    ) -> AsyncIterator[dict[str, Any] | Any]:
        return self.agent.astream(
            input=self._prepare_input(user_query, organization_info, organization_id),
            config={"callbacks": self.callbacks, "recursion_limit": 50},
            stream_mode=["values", "messages"],
        )


contacts_graph = ContactsAgent(model_name="gpt-4.1-mini").agent


if __name__ == "__main__":
    dotenv.load_dotenv()

    # configure logging
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    logging.getLogger("httpx").setLevel(logging.WARNING)

    async def main():
        agent = ContactsAgent(model_name="gpt-4.1-mini")
        #     user_query = """
        #     作为映翰通网络公司的销售人员，你在销售你的企业网络解决方案，如 ER 系列路由器。你期望能够利用运营商 ISP 的关系，与他们达成合作，这样可以让他们在向他们的客户售卖网络服务时可以与你的路由器一块捆绑销售。你现在有 linkedin 对应ISP 的信息，你将如何寻找到合适的 ISP 下的联系人，以便可以通过他达成这个合作？你的任务是根据 linkedin 上的信息，找到合适的联系人。  # noqa: E501
        # """
        user_query = """
我们为医疗和实验室冷柜提供远程监控解决方案，客户可能需要工业物联网网关等设备。
        """
        astream = await agent.astream(
            user_query=user_query,
            organization_id="54a1230d69702da4258f6903",
            organization_info="""Owens & Minor is a prominent healthcare services and solutions company, founded in 1882 in Richmond, Virginia. Originally a pharmaceutical wholesaler, it has evolved into a major provider of medical and surgical supplies, third-party logistics, and supply chain management solutions. Headquartered in Mechanicsville, Virginia, Owens & Minor is recognized as a Fortune 500 company with a workforce of over 15,000 employees globally.
The company offers a diverse range of healthcare products and services, including comprehensive supply chain management through its OMSolutions consulting services. Owens & Minor supports a wide array of healthcare providers, such as hospitals and clinics, ensuring efficient distribution of medical supplies. It operates alongside several affiliated brands, including Apria, Byram, and HALYARD, enhancing its offerings in the healthcare sector. Owens & Minor is dedicated to fostering strong relationships with its customers and communities while focusing on teamwork and continuous improvement.""",
        )
        result = ""
        async for stream_mode, chunk in astream:
            # print(type, value)
            # type, value = chunk
            if stream_mode == "values":
                message = chunk["messages"][-1]
                if isinstance(message, tuple):
                    print(f"tuple:\n{message}")
                    pass
                elif isinstance(message, BaseMessage):
                    result = message.content
                    if not isinstance(message, ToolMessage):
                        print(f"BaseMessage:\n{message.pretty_repr()}")
                        pass
                    elif message.status == "error":
                        print(f"BaseMessage:\n{message.pretty_repr()}")
                        pass
            elif stream_mode == "messages":
                message = chunk[0]
                if isinstance(message, AIMessageChunk):
                    if message.content:
                        print(
                            message.content if isinstance(message.content, str) else message.content[0].get("text", ""),
                            end="",
                            flush=True,
                        )
                    # elif message.tool_call_chunks:
                    #     for tool_call in message.tool_call_chunks:
                    #         if "name" in tool_call and tool_call["name"] is not None:
                    #             print(f"Tool Call: {tool_call['name']}\n\tArgs: ")
                    #         if "args" in tool_call and tool_call["args"] is not None:
                    #             print(tool_call["args"], end="", flush=True)
                    # else:
                    #     if "finish_reason" in message.response_metadata:
                    #         print("")
                elif isinstance(message, ToolMessage):
                    # message.pretty_print()
                    print(f"{message.pretty_repr()}")
                    pass
                else:
                    print(f"messages:\n{message.pretty_repr()}")
                    pass
            else:
                print("======Unknown========")
                print(stream_mode, chunk)
                print("=====================")
        print(f"ainvoke result: {result}")

    asyncio.run(main())
