import asyncio
from typing import Annotated, cast

from langchain_core.messages import AIMessageChunk, BaseMessage, ToolMessage
from loguru import logger
from taskiq import Context, TaskiqDepends
from taskiq.api.receiver import run_receiver_task
from taskiq.depends.progress_tracker import ProgressTracker
from taskiq.serializers import MSGPackSerializer
from taskiq_redis import RedisAsyncResultBackend, RedisStreamBroker

from research.agent_outreach import create_agent
from research.task_manager import task_manager

result_backend = RedisAsyncResultBackend(
    redis_url="redis://localhost:6379",
    prefix_str="taskiq",
    serializer=MSGPackSerializer(),
    result_ex_time=3600 * 24 * 30,
)

# Or you can use PubSubBroker if you need broadcasting
# Or ListQueueBroker if you don't want acknowledges
broker = RedisStreamBroker(
    url="redis://localhost:6379",
).with_result_backend(result_backend)

# 添加任务流管理中间件
broker.add_middlewares(task_manager)


@broker.task
async def contacts_agent_task(
    company_name: str | None = None,
    region: str | None = None,
    requirements: str | None = "",
    context: Annotated[Context, TaskiqDepends()] = None,
    progress_tracker: Annotated[ProgressTracker, TaskiqDepends()] = None,
) -> str:
    task_id = context.message.task_id
    query = f"""
目标公司：{company_name or "Not specified"}
目标地区：{region or "Not specified"}
要求：{requirements}
请根据以上信息，给出潜在的联系人信息
"""
    task_logger = logger.bind(task_id=task_id)
    task_logger.info(f"Starting contacts_agent_task, {query=}")

    await progress_tracker.set_progress(state="RUNNING", meta={"current_step": "started"})

    # 不需要手动创建任务队列，中间件会处理
    # 不需要发送任务开始消息，中间件会处理

    # 使用 agent_outreach.py 中的 create_agent 函数代替直接初始化 ContactsAgent
    try:
        # 创建代理
        agent_executor = await create_agent(model_name="gpt-4.1-mini")

        # 使用 astream_events 获取事件流
        stream = agent_executor.astream_events(
            input={"messages": [{"role": "user", "content": query}]},
            config={"recursion_limit": 50},
            include_types=["chat_model", "chain", "tool", "custom", "thinking"],
        )

        async def log_stream_handler():
            async for message in task_manager.subscribe(task_id):
                task_logger.info(f"{message}")

        asyncio.create_task(log_stream_handler())

        result = ""
        async for event in stream:
            match event["event"]:
                case "on_chat_model_stream":
                    chunk = cast(AIMessageChunk, event["data"]["chunk"])
                    if chunk.content:
                        # 发送流式内容更新
                        continue
                        task_manager.send_message(task_id, {"type": "chunk", "content": chunk.content})
                case "on_tool_start":
                    match event["name"]:
                        case "think":
                            if "thought" in event["data"]["input"]:
                                thought = event["data"]["input"]["thought"]
                                # 发送思考过程
                                task_manager.send_message(task_id, {"type": "thinking", "content": thought})
                                await progress_tracker.set_progress(
                                    state="RUNNING", meta={"current_step": f"thinking: {thought}"}
                                )
                        case _:
                            tool_name = event["name"]
                            tool_input = event["data"]["input"]
                            # 发送工具使用信息
                            task_manager.send_message(
                                task_id, {"type": "tool_call", "name": tool_name, "input": tool_input}
                            )
                            await progress_tracker.set_progress(
                                state="RUNNING", meta={"current_step": f"tool call: {tool_name}({tool_input})"}
                            )
                case "on_tool_end":
                    if event.get("data", {}).get("output"):
                        output = event["data"]["output"]
                        if isinstance(output, ToolMessage):
                            output = output.content
                        if len(output) > 0:
                            # 保留最后工具输出作为任务结果
                            result = output
                            # 发送工具输出结果
                            tool_name = event.get("name", "unknown")
                            output_str = output[:200] + ("..." if len(output) > 200 else "")
                            task_manager.send_message(
                                task_id,
                                {
                                    "type": "tool_result",
                                    "name": tool_name,
                                    "output": output_str,
                                },
                            )
                            await progress_tracker.set_progress(
                                state="RUNNING", meta={"current_step": f"tool result: {tool_name}: {output_str}"}
                            )
                case "on_chain_end":
                    # 捕获最终输出结果
                    output = event.get("data", {}).get("output")
                    if isinstance(output, dict):
                        if output.get("messages", []):
                            messages = output["messages"]
                            if messages and isinstance(messages[-1], BaseMessage):
                                result = messages[-1].content
                    elif isinstance(output, BaseMessage):
                        result = output.content
                case _:
                    # 其他事件类型忽略
                    pass

        task_logger.info("task finished")

        return result

    except Exception as e:
        error_message = f"task error: {str(e)}"
        task_logger.error(error_message)
        raise e


receiver_task: asyncio.Task | None = None


async def startup():
    await broker.startup()
    receiver_task = asyncio.create_task(
        coro=run_receiver_task(
            broker=broker,
            max_async_tasks=2,
            validate_params=True,
            max_prefetch=0,
            propagate_exceptions=True,
        )
    )

    receiver_task.add_done_callback(lambda _: print("receiver_task done"))


async def shutdown():
    if receiver_task:
        receiver_task.cancel()
    await broker.shutdown()
