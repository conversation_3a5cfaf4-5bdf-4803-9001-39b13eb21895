from contextvars import ContextVar
from typing import Any, Optional

from langchain_core.tools import BaseTool
from langchain_core.tracers.context import register_configure_hook
from langfuse.langchain import CallbackHandler
from pydantic import BaseModel

from .env import from_env, load_env, secret_from_env
from .logger import configure_loguru
from .models import init_model

__all__ = ["init_model", "load_env", "from_env", "secret_from_env", "configure_loguru"]


configure_loguru()


langfuse_callback_var: ContextVar[Optional[Any]] = ContextVar("langfuse_callback", default=None)
register_configure_hook(
    langfuse_callback_var, handle_class=CallbackHandler, env_var="LANGFUSE_PUBLIC_KEY", inheritable=True
)

# Override BaseTool.tool_call_schema to add explanation parameter as the first argument
# store original tool_call_schema property
_original_tool_call_schema = BaseTool.tool_call_schema.fget
explanation_description = """Describe your current situation in 1-2 coherent, concise, and logically clear sentences:
what information you see, how you think based on this information, why this tool is being used, and how it contributes to the goal."""  # noqa: E501


@property
def _custom_tool_call_schema(self):
    """Override tool_call_schema to add explanation parameter as the first argument"""
    original_schema = _original_tool_call_schema(self)

    if issubclass(original_schema, BaseModel):
        original_schema = original_schema.model_json_schema()

    # if it's a dict schema, modify it directly
    if isinstance(original_schema, dict):
        properties = original_schema.get("properties", {}).copy()
        if "explanation" in properties:
            properties["explanation"]["description"] = explanation_description
        else:
            properties = {
                "explanation": {
                    "type": "string",
                    "description": explanation_description,
                },
                **properties,
            }

        # update the schema
        modified_schema = original_schema.copy()
        modified_schema["properties"] = properties

        # update required fields to include explanation as first
        if "required" in original_schema:
            required = ["explanation"] + [field for field in original_schema["required"] if field != "explanation"]
            modified_schema["required"] = required
        else:
            modified_schema["required"] = ["explanation"]

        return modified_schema

    return original_schema


# apply the override
BaseTool.tool_call_schema = _custom_tool_call_schema
