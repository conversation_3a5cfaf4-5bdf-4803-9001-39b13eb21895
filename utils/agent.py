import json
from typing import Any, AsyncIterator, cast

from langchain_core.language_models.chat_models import BaseMessageChunk
from langchain_core.messages import AIMessage, BaseMessage
from langchain_core.runnables.schema import StreamEvent

from .logging_callback_handler import <PERSON>ggingCallbackHandler


async def print_stream_events(stream: AsyncIterator[StreamEvent]) -> Any:
    """Process agent astream_events and handle different event types.

    Args:
        stream: async stream from agent.astream_events()
    """
    handler = LoggingCallbackHandler()
    return_value = None

    async for stream_event in stream:
        data = stream_event["data"]
        run_id = stream_event["run_id"]
        match stream_event["event"]:
            case "on_chat_model_stream":
                chunk = cast(BaseMessageChunk, data["chunk"])
                handler.on_message_chunk(chunk)

            case "on_chat_model_end":
                message = cast(AIMessage, data["output"])
                handler.on_chat_model_end(message)

            case "on_tool_start":
                await handler.on_tool_start(
                    {"name": stream_event["name"]},
                    json.dumps(data["input"], ensure_ascii=False),
                    run_id=run_id,
                    inputs=data["input"],
                    name=stream_event["name"],
                )

            case "on_tool_end":
                await handler.on_tool_end(data["output"], run_id=run_id)

            case "on_chain_end":
                return_value = data["output"]

            case "on_custom_event":
                await handler.on_custom_event(stream_event.get("name", "custom"), data, run_id=run_id)
            case _:
                pass

    return return_value


async def print_stream(stream: AsyncIterator[BaseMessageChunk]) -> Any:
    """Print stream events to console.

    Args:
        stream: async stream from llm.astream
    """
    handler = LoggingCallbackHandler()
    async for chunk in stream:
        handler.on_message_chunk(chunk)
