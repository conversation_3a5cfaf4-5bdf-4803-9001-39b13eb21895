import json
from contextlib import contextmanager
from contextvars import ContextVar
from json import JSONDecodeError
from typing import Any, AsyncIterator, Generator, Iterator, Optional, TypeVar, Union, cast, override
from uuid import UUID

from langchain_core.callbacks.base import Async<PERSON>allbackHandler
from langchain_core.messages import AIMessage, BaseMessage, BaseMessageChunk, ToolMessage
from langchain_core.messages.base import get_msg_title_repr
from langchain_core.messages.tool import ToolCallChunk
from langchain_core.outputs import ChatGenerationChunk, GenerationChunk, LLMResult
from langchain_core.tracers._streaming import _StreamingCallbackHandler
from langchain_core.tracers.context import register_configure_hook
from loguru import logger

from .messages import message_content, message_thinking_content

T = TypeVar("T")


def tavily_search_pretty_repr(content: str) -> str:
    """Pretty print tavily search result."""
    result: dict = {}
    try:
        result = json.loads(content)
    except JSONDecodeError:
        return content

    if isinstance(result, list):
        results = result
    else:
        results = result.get("results", [])

    if not results:
        return "No search results found."

    # format main search results
    formatted_results = []
    for i, result in enumerate(results[:3], 1):  # show top 3 results
        title = result.get("title", "No title")
        url = result.get("url", "No URL")
        content_snippet = (
            result.get("content", "No content")[:200] + "..."
            if len(result.get("content", "")) > 200
            else result.get("content", "")
        )
        score = result.get("score", 0)

        formatted_result = f"{i}. {title}\n   URL: {url}\n   Content: {content_snippet}\n   Score: {score:.3f}"
        formatted_results.append(formatted_result)

    if isinstance(result, list):
        return "\n\n".join(formatted_results)

    # add query info
    query = result.get("query", "Unknown query")
    response_time = result.get("response_time", 0)

    # combine all info
    result_text = f"Tavily Search Result:\nQuery: {query}\n"
    if "answer" in result:
        result_text += f"Answer: {result.get('answer', 'No answer')}\n"
    if "follow_up_questions" in result:
        result_text += f"Follow Up Questions: {result.get('follow_up_questions')}"
    result_text += f"Response Time: {response_time:.2f}s\n"
    result_text += f"Total Results: {len(results)}\n\n"
    result_text += "\n\n".join(formatted_results)

    # add images info if available
    images = result.get("images", [])
    if images:
        result_text += f"\n\nImages found: {len(images)}"

    return result_text


def get_message_finish_reason(message: AIMessage) -> str:
    """Get the finish reason of a message."""
    if message.response_metadata and "finish_reason" in message.response_metadata:
        return message.response_metadata["finish_reason"]
    return "unknown"


class LoggingCallbackHandler(AsyncCallbackHandler, _StreamingCallbackHandler):
    """A callback handler that prints streaming events in a human friendly way."""

    last_message_chunk: Optional[BaseMessageChunk] = None
    last_ai_message: Optional[AIMessage] = None
    is_thinking: bool = False
    logger = logger.opt(colors=True)

    async def tap_output_aiter(self, run_id: UUID, output: AsyncIterator[T]) -> AsyncIterator[T]:
        async for chunk in output:
            yield chunk

    def tap_output_iter(self, run_id: UUID, output: Iterator[T]) -> Iterator[T]:
        return output

    def on_message_chunk(self, chunk: BaseMessageChunk) -> None:
        """Handle a message chunk."""
        # print(chunk)
        if not self.last_message_chunk:
            # start of new message chunk
            self.last_message_chunk = chunk
            self.logger.debug(f"<light-white>\n{get_msg_title_repr('AI Message Chunk')}</light-white>")
        elif self.last_message_chunk.id != chunk.id:
            # printing a streaming chunk, skip the parallel AI message stream
            # to avoid mixing up the message content of parallel stream
            return

        thinking = message_thinking_content(chunk)
        if thinking is not None and not self.is_thinking:
            # o3 start of thinking
            self.is_thinking = True
            print("<think>", flush=True)

        if thinking:
            print(thinking, end="", flush=True)

        content = chunk.text()
        # handle the thinking end
        if (content or chunk.tool_call_chunks) and self.is_thinking:
            self.is_thinking = False
            print("\n</think>\n", flush=True)

        if content:
            print(content, end="", flush=True)

        if chunk.tool_call_chunks:
            self._handle_tool_call_chunks(chunk.tool_call_chunks)

        if chunk.response_metadata and "model_name" in chunk.response_metadata:
            print("\n")
            self.last_message_chunk = None

    def _handle_tool_call_chunks(self, tool_call_chunks: list[ToolCallChunk]) -> None:
        """Handle tool call chunks in message chunk.

        Args:
            tool_call_chunks: list of tool call chunks
        """
        for tool_call_chunk in tool_call_chunks:
            if tool_call_chunk["name"]:
                print(
                    f"\nTool Name: {tool_call_chunk['name']} ({tool_call_chunk['id']})\nArgs:\n  ",
                    end="",
                    flush=True,
                )
            if tool_call_chunk["args"]:
                # decode unicode escape sequences in the args
                args = tool_call_chunk["args"]
                if "\\u" in args:
                    args = args.encode().decode("unicode_escape")
                print(args, end="", flush=True)

    @override
    async def on_llm_new_token(
        self,
        token: str,
        *,
        chunk: Optional[Union[GenerationChunk, ChatGenerationChunk]] = None,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> Any:
        if isinstance(chunk, ChatGenerationChunk):
            message_chunk = cast("ChatGenerationChunk", chunk).message
            self.on_message_chunk(message_chunk)

    @override
    async def on_llm_end(
        self, response: LLMResult, *, run_id: UUID, parent_run_id: Optional[UUID] = None, **kwargs: Any
    ) -> Any:
        message: BaseMessage | None = None
        for gen in response.generations:
            for chunk in gen:
                message = chunk.message
                break
        if message and isinstance(message, AIMessage):
            self.on_chat_model_end(message)

    async def on_chat_model_start(
        self,
        serialized: dict[str, Any],
        messages: list[list[BaseMessage]],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        pass

    def on_chat_model_end(self, message: AIMessage) -> None:
        """Handle completion of a chat model message."""
        if self.last_message_chunk and message.id == self.last_message_chunk.id:
            self.last_message_chunk = None

        self.last_ai_message = message

        if message.tool_calls:
            # check for special 'think' tool
            if message.tool_calls[0]["name"] == "think":
                return

            content = AIMessage(content="", tool_calls=message.tool_calls).pretty_repr()
            self.logger.info(f"\n<cyan>{content.replace('<', '\\<')}</cyan>")
        else:
            content = message_content(message).replace("<", "\\<")
            if content:
                self.logger.info(f"\n<light-blue>{get_msg_title_repr('AI Message')}\n{content}</light-blue>")

        stop_reason = get_message_finish_reason(message)
        if stop_reason and stop_reason.lower() not in ["stop", "tool_calls", "unknown"]:
            finish_message = message.response_metadata.get("finish_reason_message", "")
            self.logger.warning(
                f"AI message finished with reason: {stop_reason}" + (f", {finish_message}" if finish_message else "")
            )

    @override
    async def on_tool_start(
        self,
        serialized: dict[str, Any],
        input_str: str,
        *,
        run_id: UUID,
        inputs: Optional[dict[str, Any]] = None,
        name: Optional[str] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle the start of a tool call."""
        if name == "think":
            self.logger.info(f"<light-black>\\<think>{inputs['thought']}\\</think></light-black>")

    @override
    async def on_tool_end(self, output: Any, *, run_id: UUID, **kwargs: Any) -> None:
        """Handle the end of a tool call."""
        if isinstance(output, ToolMessage):
            tool_name = output.name

            if tool_name == "tavily_search":
                self.logger.info(f"Tool {tool_name} result:\n<cyan>{tavily_search_pretty_repr(output.content)}</cyan>")
            elif output.name not in ["think", "final_answer"]:
                if self.last_ai_message and self.last_ai_message.content == output.content:
                    return
                content_brief = output.content[:2048] + "..." if len(output.content) > 2048 else output.content
                content_brief = content_brief.replace("<", "\\<")
                # self.logger.info(f"Tool {output.name} result:\n<cyan>{content_brief}</cyan>")
                tool_message = ToolMessage(
                    content=content_brief,
                    name=output.name,
                    id=output.id,
                    tool_call_id=output.tool_call_id,
                )
                self.logger.info(f"\n<cyan>{tool_message.pretty_repr()}</cyan>")

    # ------------------------------------------------------------------
    # custom event hook -------------------------------------------------
    # ------------------------------------------------------------------
    @override
    async def on_custom_event(
        self,
        name: str,
        data: Any,
        *,
        run_id: UUID,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """Handle any custom event emitted by the runnable stack."""
        try:
            for item in data:
                self.logger.info(f"<light-black>custom event: {item}</light-black>")
        except Exception as exc:  # pragma: no cover – never crash caller
            self.logger.warning(f"LoggingCallbackHandler failed to handle custom event {name}: {exc}", exc_info=True)


logging_callback_var: ContextVar[Optional[LoggingCallbackHandler]] = ContextVar("logging_callback", default=None)
register_configure_hook(logging_callback_var, True)


@contextmanager
def logging_callback() -> Generator[LoggingCallbackHandler, None, None]:
    """enable the LoggingCallbackHandler in a context manager.

    Example:
        >>> with logging_callback():
        ...     # Use the LoggingCallbackHandler
    """
    cb = LoggingCallbackHandler()
    logging_callback_var.set(cb)
    yield cb
    logging_callback_var.set(None)
