import os
from typing import Literal

from langchain.chat_models import init_chat_model
from langchain.chat_models.base import BaseChatModel
from loguru import logger

from utils import from_env, secret_from_env

type ModelAlias = Literal[
    "claude-4-sonnet",
    "claude-3-7-sonnet",
    "claude-3.7-sonnet",
    "claude-3.5-haiku",
    "qwen",
    "gemini-2.5-flash",
    "gemini-2.5-pro",
    "o4-mini-high",
]

logged_messages = set()


def _log_once(message: str, level: str = "DEBUG"):
    if message not in logged_messages:
        logged_messages.add(message)
        logger.log(level, message)


def _get_default_thinking_budget(thinking_budget: int | None = None) -> int | None:
    if thinking_budget is not None:
        return int(thinking_budget)
    budget = from_env("LLM_THINKING_BUDGET", default=None)
    return int(budget) if budget else None


def _claude_thinking_value(thinking_budget: int | None = None) -> dict:
    budget_tokens = _get_default_thinking_budget(thinking_budget)
    match budget_tokens:
        case 0:
            return {"type": "disabled"}
        case budget_tokens if budget_tokens < 1024:
            return {"type": "enabled", "budget_tokens": 1024}
        case _:
            return {"type": "enabled", "budget_tokens": budget_tokens}


def _claude_default_thinking_params(thinking_budget: int | None = None) -> dict:
    return {
        "temperature": 1,
        "additional_model_request_fields": {
            "thinking": _claude_thinking_value(thinking_budget),
        },
    }


def _parse_model_alias(model: str, **kwargs) -> str | tuple[str, dict]:
    """parse model alias to actual model name"""
    thinking_budget = kwargs.get("thinking_budget")
    match model:
        case "claude-4-sonnet-thinking":
            return "us.anthropic.claude-sonnet-4-20250514-v1:0", _claude_default_thinking_params(thinking_budget)
        case "claude-4-sonnet":
            return "us.anthropic.claude-sonnet-4-20250514-v1:0"
        case "claude-3-7-sonnet" | "claude-3.7-sonnet":
            return "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
        case "claude-3.7-sonnet-thinking":
            return "us.anthropic.claude-3-7-sonnet-20250219-v1:0", _claude_default_thinking_params(thinking_budget)
        case "claude-3.5-haiku":
            return "us.anthropic.claude-3-5-haiku-20241022-v1:0"
        case "qwen":
            return "qwen-max"
        # case "gemini-2.5-flash":
        #     return "gemini-2.5-flash-preview-05-20"
        # case "gemini-2.5-pro":
        #     return "gemini-2.5-pro-preview-06-05"
        # return "gemini-2.5-pro-preview-05-06"
        # return "gemini-2.5-pro-preview-03-25"
        case "gemini-2.5-flash-lite":
            return "gemini-2.5-flash-lite-preview-06-17"
        case model if model.startswith("openrouter:"):
            return model.split(":", 1)[1], {
                "model_provider": "openai",
                "base_url": "https://openrouter.ai/api/v1",
                "api_key": secret_from_env("OPENROUTER_API_KEY", default=None),
            }
        case model if model.startswith("aliyun:"):
            return model.split(":", 1)[1], {
                "model_provider": "openai",
                "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                "api_key": secret_from_env("DASHSCOPE_API_KEY", default=None),
            }
        case model if model.startswith("o"):
            use_responses_api = kwargs.get("use_responses_api", False)
            reasoning_effort = "medium"
            reasoning_summary = "auto" if use_responses_api else None
            if model.endswith("-high"):
                reasoning_effort = "high"
                model = model[:-5]
            elif model.endswith("-low"):
                reasoning_effort = "low"
                model = model[:-4]

            return model, {"reasoning_effort": reasoning_effort} if not use_responses_api else {
                "model_kwargs": {
                    "reasoning": {"effort": reasoning_effort, "summary": reasoning_summary},
                }
            }
        case model if model.startswith("qwen") and model.endswith("-thinking"):
            return model[:-9], {
                "extra_body": {
                    "enable_thinking": True,
                    "thinking_budget": _get_default_thinking_budget(thinking_budget),
                }
            }
        case _:
            return model


def _parse_model_provider(model: str, **kwargs: dict) -> str | tuple[str, dict]:
    """parse model to determine the appropriate model provider and its params"""
    if "claude" in model:
        https_proxy = from_env(["HTTPS_PROXY", "OPENAI_PROXY"], default="")
        _log_once(f"using http proxy: {https_proxy} to access anthropic model {model}")
        from botocore.config import Config

        return "bedrock_converse", {
            "provider": "anthropic",
            # "credentials_profile_name": from_env("AWS_PROFILE", default="inhand"),
            "aws_access_key_id": from_env("AWS_ACCESS_KEY_ID", default=None),
            "aws_secret_access_key": secret_from_env("AWS_SECRET_ACCESS_KEY", default=None),
            "region_name": from_env("AWS_REGION", default="us-west-2"),
            "config": Config(proxies={"https": https_proxy}),
        }
    elif model.startswith("qwen") or model.startswith("deepseek"):
        return "openai", {
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "api_key": secret_from_env("DASHSCOPE_API_KEY", default=None),
            "openai_proxy": "",
        }
    elif model.startswith("gemini"):
        grpc_proxy = from_env(["GRPC_PROXY", "OPENAI_PROXY"], default="")
        if grpc_proxy:
            _log_once(f"using grpc proxy: {grpc_proxy} to access google model {model}")
            os.environ["grpc_proxy"] = grpc_proxy
        thinking_budget = _get_default_thinking_budget(kwargs.get("thinking_budget"))
        return "google_genai", {
            "thinking_budget": thinking_budget,
            "include_thoughts": True,
        }
    elif model.startswith(("gpt", "o")):
        return "openai"
    else:
        # default to openai for unknown models
        return None


def _init_model_params(model: str, thinking_budget: int | None = None, **kwargs: dict) -> tuple[str, dict]:
    """initialize model parameters based on model type"""
    match _parse_model_alias(model, thinking_budget=thinking_budget, **kwargs):
        case (model_name, alias_params):
            model = model_name
            kwargs = alias_params | kwargs
        case model_name:
            model = model_name

    if "model_provider" not in kwargs:
        match _parse_model_provider(model, thinking_budget=thinking_budget, **kwargs):
            case (model_provider, provider_params):
                kwargs = provider_params | kwargs
                # the model provider may override the model name
                if "model" in provider_params:
                    model = provider_params["model"]
            case model_provider:
                pass

        kwargs["model_provider"] = model_provider
    else:
        model_provider = kwargs["model_provider"]

    if "max_tokens" not in kwargs:
        kwargs["max_tokens"] = int(from_env("LLM_MAX_TOKENS", default="2048"))

    if "temperature" not in kwargs:
        kwargs["temperature"] = float(from_env("LLM_TEMPERATURE", default="0"))

    # handle special cases
    if model_provider == "openai" and model.startswith("o"):
        # openai reasoning models should not have temperature
        kwargs.pop("temperature", None)

    return model, kwargs


def init_model(
    model: ModelAlias | str,
    thinking_budget: int | None = None,
    **kwargs,
) -> BaseChatModel:
    """
    initialize a chat model with the given parameters

    Args:
        model: model name or alias
        use_responses_api: optional flag to use responses api for reasoning models, used for openai models only, default to False
        thinking_budget: thinking budget in tokens, set to 0 to disable thinking, default to None

    Returns:
        BaseChatModel: initialized chat model
    """  # noqa: E501
    model_name, init_model_params = _init_model_params(
        model=model,
        thinking_budget=thinking_budget,
        **kwargs,
    )
    logger.debug(f"initializing model {model_name} with params {init_model_params}")
    llm = init_chat_model(model=model_name, **init_model_params)
    if "_model_name" not in llm:
        llm._model_name = model_name
    return llm
