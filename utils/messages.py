from langchain_core.messages import AIMessage, BaseMessage


def message_thinking_content(message: BaseMessage) -> str | None:
    """Get the thinking content of the message.

    Args:
        message: The message to extract thinking content from
    """
    text_parts = []
    # openai o-series model reasoning content
    if "reasoning" in message.additional_kwargs:
        reasoning = message.additional_kwargs["reasoning"]
        if isinstance(reasoning, dict):
            reasoning_summary = reasoning.get("summary", [])
            for summary in reasoning_summary:
                if isinstance(summary, dict) and "text" in summary:
                    text_parts.append(summary["text"])
        elif isinstance(reasoning, str):
            text_parts.append(reasoning)

    if isinstance(message.content, list):
        for block in message.content:
            if not isinstance(block, dict):
                continue

            block_type = block.get("type", None)
            if block_type == "thinking":
                # Google Gemini thinking block
                text_parts.append(block.get("thinking", ""))
            elif block_type == "reasoning_content":
                # anthropic claude thinking block
                reasoning_content = block.get("reasoning_content", {})
                text_parts.append(reasoning_content.get("text", ""))

    return "\n".join(text_parts) if text_parts else None


def message_content(message: BaseMessage) -> str:
    """Get the text content of the message，this function will add <think> tag if the message is thinking.
    if you don't want to show the thinking content, you can use message.text() instead.

    Args:
        message: The message to extract content from

    Returns:
        The text content of the message.
    """
    thinking = message_thinking_content(message)
    thinking = f"<think>\n{thinking}\n</think>\n" if thinking else ""
    return thinking + message.text()
