from langchain_core.messages import BaseMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import chain
from pydantic import BaseModel

from .models import init_model


@chain
def context_to_str(input: str | BaseMessage) -> str:
    if isinstance(input, BaseMessage):
        return input.text()
    return input


async def extract_structure_response(context: str | BaseMessage, schema: dict | type) -> BaseModel | dict:
    llm = init_model("gpt-4.1-mini").with_structured_output(schema)
    prompt = ChatPromptTemplate.from_template(
        "Here is the content of the section:"
        "----------------\n{context}\n----------------"
        "Given the contextual information, extract out a object that matches the schema."
    )
    chain = context_to_str | prompt | llm
    return await chain.ainvoke(context)


async def extract_title(context: str | BaseMessage) -> str:
    llm = init_model("gpt-4.1-mini")

    prompt = ChatPromptTemplate.from_template(
        "Here is the content of the section:"
        "----------------\n{context}\n----------------"
        "Give a title that summarizes all of the unique entities, titles or themes found in the context. Title:"
    )
    chain = context_to_str | prompt | llm | StrOutputParser()
    return await chain.ainvoke(context)


async def extract_summary(context: str | BaseMessage) -> str:
    llm = init_model("gpt-4.1-mini")

    prompt = ChatPromptTemplate.from_template(
        "Here is the content of the section:"
        "----------------\n{context}\n----------------"
        "Summarize the key topics and entities of the section. Summary:"
    )
    chain = context_to_str | prompt | llm | StrOutputParser()
    summary = await chain.ainvoke(context)
    # remove the "Summary:" prefix
    return summary.removeprefix("Summary:").strip()
