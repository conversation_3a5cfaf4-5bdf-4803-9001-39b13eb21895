import os
from collections import defaultdict

import chromadb
import httpx
from chromadb.api.types import QueryResult
from chromadb.config import Settings
from chromadb.utils import embedding_functions

from utils.env import load_env


async def test_embedding():
    load_env()

    # create openai embedding function
    openai_ef = embedding_functions.OpenAIEmbeddingFunction(
        api_key=os.getenv("OPENAI_API_KEY"),
        model_name="text-embedding-3-small",  # or text-embedding-ada-002
    )

    openai_ef.client._client = httpx.Client(base_url="https://api.openai.com/v1", proxy="http://127.0.0.1:1087")

    # create chromadb client with in-memory database
    client = chromadb.Client(
        Settings(
            # chroma_db_impl="duckdb+parquet",
            # persist_directory=None,  # in-memory for testing
        )
    )

    # create or get collection with openai embedding function
    collection_name = "test_documents"
    collection = client.create_collection(
        name=collection_name,
        embedding_function=openai_ef,
        metadata={"hnsw:space": "cosine"},  # use cosine similarity
    )

    # prepare test data - organized by functional modules
    # each module can have multiple documents
    module_documents = {
        "link_quality": [
            """上行链路质量监控系统通过多维度网络性能指标实时评估WAN接口和蜂窝网络连接的可用性状态。系统采用主动探测机制，持续监控链路的延迟、抖动、丢包率和信号强度等关键性能参数，当任一指标超过预设阈值并持续达到配置的时间窗口时，自动判定链路为不可用状态。监控引擎支持针对不同网络类型的差异化检测策略，确保链路状态评估的准确性和及时性。

上行链路管理功能提供故障切换和负载均衡两种工作模式，支持多WAN接口的智能调度和流量分配。在故障切换模式下，系统根据接口优先级建立主备链路关系，当主链路发生故障时按照配置的切换延迟自动切换到备份链路。负载均衡模式则实现流量在多个可用链路间的动态分配，提升网络带宽利用率和连接可靠性。系统支持自定义探测目标地址，默认使用上行接口获取的DNS服务器地址进行连通性检测。

链路检测机制采用ICMP探测和SLA监控相结合的方式，通过配置主备探测目标实现链路可达性的双重验证。系统支持延迟阈值监控，当RTT超过设定值并持续指定时间后触发链路状态变更。抖动监控功能评估网络延迟的稳定性，通过统计分析识别网络质量波动。丢包率监控实时统计数据包传输成功率，为链路质量评估提供可靠依据。

蜂窝网络专用的信号强度监控功能根据无线信号质量等级判断移动网络连接的稳定性。系统将信号强度划分为四个等级，从差到极好对应不同的服务质量水平。当信号强度低于配置阈值并持续超过设定时间时，系统自动将该蜂窝链路标记为不可用状态，触发链路切换或流量重分配机制，确保网络连接的持续可用性。""",
            # "上行链路，链路优先级，上行接口优先级，上行链路探测，链路质量探测",
            # "链路切换，Failover， 链路备份， load balance",
        ],
        "ip_passthrough": [
            """IP透传（IP Passthrough）是一种特殊的网络桥接技术，通过将上行链路获取的公网IP地址直接透传给下行终端设备，实现终端设备与上游网络的直接IP层连接。该功能绕过了传统的NAT地址转换机制，使终端设备能够直接使用运营商分配的公网IP地址进行网络通信，消除了NAT转换带来的性能损耗和协议兼容性问题。系统支持WAN1以太网接口和cellular1蜂窝网络接口作为上行链路源。

透传机制采用MAC地址绑定和DHCP动态分配相结合的方式实现精确的IP地址分发控制。通过bind_mac参数可以将透传的IP地址绑定到特定MAC地址的终端设备，确保IP地址分配的确定性和安全性。系统内置DHCP服务器负责向下行终端推送透传的IP地址，支持2分钟到7天（10080分钟）的灵活租期配置，满足不同应用场景的地址管理需求。

上下行接口配置遵循严格的接口映射规则，当上行链路选择WAN1时，下行接口仅支持LAN2端口；当上行链路选择蜂窝网络或保持默认配置时，下行接口可选择LAN1或LAN2端口。prefix_len参数控制透传IP地址的子网掩码长度，取值范围8-30位，默认16位掩码，用于定义终端设备的网络地址空间和路由范围。

IP透传功能在网络架构中实现了二层桥接和三层路由的混合模式，既保持了终端设备的公网IP直连特性，又通过设备的管理和控制功能提供网络安全和监控能力。该技术特别适用于需要公网IP直连的应用场景，如远程监控、VPN服务器、游戏服务器等对NAT穿透敏感的业务应用。""",
            # "启用或禁用IP直通功能",
            # "支持公网IP直接透传到内网设备，实现透明网络接入",
            # "IP透传, 透明网络,  DHCP服务, 公网IP, 网络透传, 接口约束, 透明接入, 网络直连, 设备透传",
            # "IPPT, IP passthrough, IP透传，MAC地址格式00-11-22-33-44-55自动转换为格式 00:11:22:33:44:55",
        ],
        "cellular": [
            """蜂窝网络调制解调器系统提供完整的移动通信接入能力，支持2G/3G/4G/5G多制式网络自适应连接。系统采用软件定义的网络类型选择机制，可根据信号质量和网络可用性自动选择最优的无线接入技术。5G网络支持独立组网（SA）和非独立组网（NSA）两种部署模式，通过灵活的模式配置适应不同运营商的网络架构。调制解调器集成NAT功能，实现内网设备的地址转换和网络隔离，支持IPv4/IPv6双栈协议和自适应MTU检测机制。

多SIM卡管理系统支持物理SIM卡和eSIM的混合部署，提供智能的SIM卡切换和负载均衡策略。系统通过优先级队列管理多张SIM卡的激活顺序，当主SIM卡连接失败或信号质量下降时，自动按照预设优先级切换到备用SIM卡。双SIM卡模式支持基于流量使用量、连接时长和时间周期的智能回切策略，确保成本控制和连接稳定性的平衡。拨号超时机制防止长时间等待无效连接，提升网络接入的响应速度。

APN配置文件管理系统为不同的SIM卡和网络场景提供差异化的接入点配置。每个配置文件包含APN名称、IP协议类型、认证方式和凭据信息，支持PAP、CHAP、MS-CHAP等多种认证协议。系统允许为每张SIM卡配置主备两套APN配置文件，当主配置文件连接失败时自动切换到备用配置文件，提升连接成功率。APN自动选择功能根据SIM卡信息和网络环境智能匹配最适合的接入点配置。

SIM卡状态管理功能提供精细化的卡片控制和监控能力。系统支持SIM卡的启用/禁用控制、PIN码验证、漫游开关和IMS服务配置。针对IoT应用场景，系统提供IMS服务的独立控制选项，优化数据传输性能和功耗管理。PDP类型配置支持IPv4、IPv6和双栈模式，适应不同运营商网络的协议要求。连接模式管理提供始终在线、按需连接和手动连接三种工作方式，满足不同应用场景的连接需求和功耗控制要求。""",
            # "蜂窝网络(Cellular)配置的JSON Schema规范，支持多SIM卡管理、网络连接配置、APN设置和双SIM卡切换策略。",
            # "调制解调器配置 (modem)，SIM1、SIM2、eSIM管理，APN配置文件 (profile)，双SIM卡切换策略",
            # "工业物联网设备网络连接，多运营商网络冗余，5G网络部署和配置，移动网络故障切换，嵌入式设备蜂窝通信",
            # "网络切换, 故障切换,移动通信, 物联网, 网络冗余, PPP认证, PDP协议, IMS系统, 漫游功能, 网络制式",
        ],
        "firewall": [
            """网络防火墙系统基于状态检测和包过滤技术实现精细化的网络访问控制，通过入站和出站规则集合对网络流量进行双向安全防护。系统采用序列号驱动的规则匹配机制，按照规则优先级顺序逐条检查数据包特征，实现高效的流量分类和处理。防火墙引擎支持多种网络协议的深度检测，包括TCP、UDP、ICMP以及自定义协议号，为不同类型的网络应用提供差异化的安全策略。

访问控制规则支持基于网络接口、协议类型、源目地址和端口范围的多维度匹配条件。系统允许配置单一IP地址、地址范围或CIDR网段作为源目标识，实现灵活的网络分段控制。端口匹配功能支持单端口、端口范围和多端口组合的复杂配置，通过逗号分隔的语法实现多个不连续端口或端口段的统一管理。规则动作包括允许和拒绝两种处理方式，结合规则启用状态控制实现动态的安全策略调整。

入站流量控制机制对来自外部网络的连接请求进行安全检查，默认采用拒绝策略确保网络边界的安全防护。系统通过精确的规则匹配识别合法的入站连接，仅允许符合安全策略的流量进入内部网络。出站流量管理功能控制内部网络向外部的访问行为，默认采用允许策略支持正常的业务通信需求，同时可通过自定义规则限制特定的出站连接。

规则处理引擎采用首次匹配即停止的算法，当数据包匹配到第一条符合条件的规则时立即执行相应动作，提升处理效率。系统支持基于网络接口的规则绑定，可针对WAN口和蜂窝网络接口配置不同的安全策略。默认规则机制为未匹配任何显式规则的流量提供兜底处理，确保防火墙策略的完整性和网络通信的可预测性。""",
            # "防火墙出站、入站配置，多协议过滤和默认策略配置",
            # "网络安全防护，访问控制列表，数据包过滤，端口管理，协议控制",
        ],
        "policy_routing": [
            """策略路由是一种基于多维度匹配条件的高级路由转发机制，通过定义精细化的流量分类规则实现智能路由决策。系统根据数据包的源地址、目标地址、协议类型、端口号等五元组信息进行流量识别，并按照预设的策略规则将匹配的流量定向转发至指定的WAN接口或蜂窝网络接口。该机制突破了传统基于目标地址的路由限制，实现了基于业务需求的灵活路由控制。

策略路由规则采用序列号优先级处理机制，支持最多255条规则的精确匹配和转发控制。每条规则包含完整的五元组匹配条件，其中协议字段支持TCP、UDP、ICMP标准协议以及1-255范围内的自定义协议号，端口配置支持单端口、端口范围和多端口组合的灵活定义方式。系统通过preferred_outif参数指定流量的首选出接口，并通过force_forward强制转发机制确保关键业务流量的路径可控性。

规则匹配引擎按照sequence序列号从小到大的顺序进行逐条匹配，一旦找到符合条件的规则即执行相应的转发动作。源地址和目标地址字段支持单IP、IP范围和CIDR子网掩码三种表示方法，为不同网络拓扑提供灵活的地址匹配能力。当启用强制转发模式时，匹配的流量将严格按照策略规则进行转发，即使指定接口不可达也不会回退到其他路由路径。

策略路由与传统静态路由和动态路由协议形成互补的多层次路由架构，在路由决策过程中具有最高优先级。该功能特别适用于多WAN环境下的负载均衡、业务隔离、带宽优化等应用场景，通过精确的流量工程实现网络资源的最优化利用和业务服务质量的差异化保障。""",
            # "策略路由, 智能路由, 流量分流, 负载均衡, 链路选择, 强制转发, 路由策略, 流量控制, 多链路, 网络优化, 路由优先级",
            # "基于流量特征的智能路由选择和强制转发控制",
            # "移动流量成本控制, 物联网设备流量管理, 嵌入式设备流量限制",
        ],
        "wireless": [
            # "Wireless AP, WiFi配置, 2.4G, 5G, 多SSID, WPA认证, 无线信道, 频宽设置, VLAN隔离, RADIUS认证, 嵌入式WiFi, 双频段, 网络隔离",
            # "无线网络接入点配置，WiFi热点管理，无线安全认证",
        ],
        "qos": [
            """QoS流量整形功能通过精细化的带宽控制和优先级管理机制，实现网络流量的智能调度和服务质量保障。该功能基于HTB（Hierarchical Token Bucket）算法，支持上行链路和下行链路的独立带宽限制，通过egress_rate和ingress_rate参数分别控制上传和下载速率，实现从1Kbps到1000Mbps的精确带宽分配。系统采用分层队列调度架构，确保关键业务流量获得优先传输保障。

上行链路QoS规则配置针对WAN口和蜂窝网络接口提供独立的带宽管理策略。通过uplink_rules对象为wan1和cellular1接口分别设置专用的带宽限制参数，支持egress_rate上行保证带宽和ingress_rate下行保证带宽的精确配置。该机制确保不同网络接口的流量隔离和带宽资源的合理分配，防止单一接口流量对整体网络性能的影响。

用户自定义QoS规则支持基于五元组（源IP、目标IP、协议、源端口、目标端口）的精细化流量分类和控制。规则匹配采用sequence序号确定的优先级顺序，支持IP地址范围、CIDR网段、端口范围等多种匹配模式。每条规则可设置priority优先级等级（highest到lowest五个级别）和DSCP标记，实现差分服务的QoS保障机制。

带宽控制机制采用保证带宽（rate）和限制带宽（ceil）的双重约束模型。egress_rate和ingress_rate定义流量的最小保证带宽，确保关键应用的基础传输需求；egress_ceil和ingress_ceil设置流量的最大限制带宽，防止单一流量占用过多网络资源。该机制支持带宽借用算法，允许空闲带宽在不同流量类别间动态分配，提高整体带宽利用效率。

DSCP标记和优先级队列调度机制实现端到端的服务质量保障。系统支持标准DSCP值配置，包括EF（46）用于VoIP语音流量、AF41（34）用于视频流量、AF31（26）用于交互式应用等。结合五级优先级队列（highest、high、medium、low、lowest），确保不同业务类型获得相应的传输优先级和延迟保障，满足实时通信和关键业务应用的QoS需求。"""
        ],
        "ipsec": [
            "在蜂窝网络环境中，IPsec VPN隧道需要适应不稳定的链路状态。配置中选择接口为cellular1，支持快速重连和DPD检测，及时发现链路故障并进行隧道重建。IKE配置采用较短的DPD间隔和超时参数，提升故障检测灵敏度。加密和认证算法选择兼顾性能和安全，确保移动环境下的连接稳定性和数据安全。",
            "在混合云架构中，企业内部网络与云服务提供商之间通过IPsec隧道实现安全连接。配置支持多子网映射，满足云端多租户和复杂网络划分需求。IKE和IPsec策略灵活配置，支持ESP和AH协议选择，满足不同安全策略要求。通过配置本地和远程ID类型为自动，简化云端身份管理。隧道启用后可通过接口绑定实现流量分流和负载均衡。",
            "IPsec VPN配置的JSON Schema规范，支持IKEv1/v2协议、多种加密算法和安全策略配置",
            "IPsec VPN, IKE协议, 安全隧道, 加密算法, 预共享密钥, DPD检测, 完美前向保密, 企业VPN, 安全通信, 网络隧道",
            "AES, SHA, DES, DH, 密钥协商, 安全认证, 密钥管理, 网络加密, 数据保护, 安全VPN, 加密隧道",
        ],
    }

    # flatten documents and create metadata with module names
    documents = []
    metadatas = []
    ids = []

    doc_id = 0
    for module_name, module_docs in module_documents.items():
        for doc in module_docs:
            split_docs = [d.strip() for d in doc.split("\n") if d.strip()]
            for split_doc in split_docs:
                documents.append(split_doc)
                metadatas.append({"module": module_name})
                ids.append(f"doc_{doc_id}")
                doc_id += 1

    # insert documents into collection
    collection.add(documents=documents, ids=ids, metadatas=metadatas)

    print(f"Inserted {len(documents)} documents into collection '{collection_name}'")
    print(f"Using OpenAI embedding model: {openai_ef.model_name}")
    print(f"Functional modules: {list(module_documents.keys())}")

    # test queries for different scenarios
    test_queries = [
        {
            "query": "请配置一个IPsec VPN隧道连接云服务，支持多子网，IKE和IPsec策略使用默认AES128和SHA1，ID类型自动，接口绑定wan1。",
            "expected_modules": ["ipsec"],
        },
        # {"query": "WiFi热点配置，无线网络管理", "expected_modules": ["wireless"]},
        # {"query": "防火墙规则配置，网络安全防护", "expected_modules": ["firewall"]},
    ]

    # questions = [
    #     "配置WAN口主备，主用宽带，备用5G，支持自动切换，切换时不中断业务",
    #     "实现WAN链路备份，主链路为有线宽带，备链路为5G蜂窝，具备无缝切换能力",
    #     "设置WAN接口故障检测与自动切换，主为有线，备为5G，切换时保持网络连接",
    # ]

    # test_queries = [{"query": query, "expected_modules": ["link_quality"]} for query in questions]

    for test_case in test_queries:
        query_text = test_case["query"]
        expected_modules = test_case["expected_modules"]

        # perform similarity search
        results: QueryResult = collection.query(
            query_texts=[query_text],
            n_results=8,  # return top 8 most similar documents
            include=["documents", "distances", "metadatas"],
        )

        print(f"\n{'=' * 80}")
        print(f"Query: '{query_text}'")
        print(f"Expected modules: {expected_modules}")
        print("Top similar documents:")

        # analyze retrieved modules
        retrieved_modules = []
        for i, (doc, distance, metadata) in enumerate(
            zip(results["documents"][0], results["distances"][0], results["metadatas"][0])
        ):
            module_name = metadata["module"]
            retrieved_modules.append(module_name)

            print(f"{i + 1}. Distance: {distance:.4f} | Module: {module_name}")
            print(f"   Document: {doc[:100]}...")
            print()

        # analyze module retrieval accuracy
        unique_retrieved_modules = list(dict.fromkeys(retrieved_modules))  # preserve order, remove duplicates
        top_3_modules = unique_retrieved_modules[:3]

        print(f"Retrieved modules (top 3): {top_3_modules}")

        # check accuracy
        correct_modules = [m for m in top_3_modules if m in expected_modules]
        accuracy = len(correct_modules) / len(expected_modules) if expected_modules else 0

        print(f"Correct modules found: {correct_modules}")
        print(f"Accuracy: {accuracy:.2%}")

        if accuracy >= 0.5:
            print("✅ Search result is GOOD - found expected modules")
        else:
            print("❌ Search result needs improvement - missed expected modules")

    # verify collection stats
    collection_count = collection.count()
    print(f"\n{'=' * 80}")
    print(f"Total documents in collection: {collection_count}")

    # show module distribution
    module_counts = defaultdict(int)
    for metadata in metadatas:
        module_counts[metadata["module"]] += 1

    print("Documents per module:")
    for module, count in module_counts.items():
        print(f"  {module}: {count} documents")

    # cleanup - delete collection
    client.delete_collection(collection_name)
    print(f"Cleaned up collection '{collection_name}'")
