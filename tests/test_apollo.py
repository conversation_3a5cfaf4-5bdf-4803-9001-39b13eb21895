from pprint import pprint

import yaml
from dotenv import load_dotenv

from agent.tools.apollo import apollo

load_dotenv()


async def test_search_organization():
    result = await apollo.search_organization(q_organization_name="https://thetechnologydoctors.co.uk")
    pprint(result)
    assert "error" not in result


async def test_get_organization_by_id():
    result = await apollo.get_organization_by_id("60b2d0545e799d0001881d19")
    pprint(result)
    assert "error" not in result

    print(yaml.dump(result, indent=2))
