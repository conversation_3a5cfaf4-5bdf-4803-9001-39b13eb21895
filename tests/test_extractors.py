from typing import TypedDict

from langchain_core.messages import HumanMessage
from pydantic import BaseModel

from utils.extractors import extract_structure_response, extract_summary, extract_title


async def test_extract_structure_response():
    class User(BaseModel):
        name: str
        age: int
        email: str
        phone: str

    response = await extract_structure_response(
        """
<PERSON> is 30 years old and his <NAME_EMAIL>. His phone number is ************.
""",
        User,
    )
    assert response is not None
    assert response.name == "<PERSON>"
    assert response.age == 30
    assert response.email == "<EMAIL>"
    assert response.phone == "************"


async def test_extract_structure_response_with_message():
    class User(BaseModel):
        name: str
        age: int
        email: str
        phone: str

    response = await extract_structure_response(
        HumanMessage(
            content="张三 is 30 years old and his <NAME_EMAIL>. His phone number is ************. "
        ),
        User,
    )
    assert response is not None
    assert response.name == "张三"


async def test_extract_structure_response_with_typed_dict():
    class User(TypedDict):
        name: str
        age: int
        email: str
        phone: str

    response = await extract_structure_response(
        """
<PERSON> is 30 years old and his <NAME_EMAIL>. His phone number is ************.
""",
        User,
    )

    assert response is not None
    assert response["name"] == "John Doe"


async def test_extract_title():
    title = await extract_title(
        """
John Doe is 30 years old and his <NAME_EMAIL>. His phone number is ************.
""",
    )
    assert title is not None
    print(title)


async def test_extract_summary():
    summary = await extract_summary(
        """
John Doe is 30 years old and his <NAME_EMAIL>. His phone number is ************.
""",
    )
    assert summary is not None
    print(summary)
