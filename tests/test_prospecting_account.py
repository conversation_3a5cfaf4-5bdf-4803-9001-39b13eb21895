from utils import load_env


async def test_prospecting_account():
    from server.account.prospecting_accounts_processor import ProspectingAccountsProcessor

    load_env()

    processor = ProspectingAccountsProcessor(task_id="test_prospecting_account")
    result = await processor.ainvoke(
        input={
            "account_id": "3091799000303764040",
            "user_query": "我想找一些像Mesta Electronics样的需要使用边缘网关产品的公司，我想把我们的边缘网关产品卖给他们，最多只调研2家公司，并只需要一个结果即可。",  # noqa: E501
        }
    )
    print(result)
