#!/usr/bin/env python3
"""
Test MCP SSE client implementation.
Tests connecting to an MCP server via SSE and listing available tools.
"""

import json
import logging
from contextlib import AsyncExitStack
from typing import Any

import httpx
from loguru import logger
from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client


class SseClientConfig:
    """Configuration for SSE client connection."""

    def __init__(self, url: str, headers: dict[str, Any] | None = None) -> None:
        """Initialize SSE client configuration.

        Args:
            url: The SSE endpoint URL
            headers: Optional headers to include in requests
        """
        self.url: str = url
        self.headers: dict[str, Any] = headers or {}


def create_insecure_http_client(
    headers: dict[str, Any] | None = None, auth: httpx.Auth | None = None, **kwargs
) -> httpx.AsyncClient:
    """Create an httpx client that skips SSL verification.

    Args:
        headers: Optional headers to include in requests
        auth: Optional authentication

    Returns:
        httpx.AsyncClient with SSL verification disabled
    """
    return httpx.AsyncClient(
        headers=headers,
        auth=auth,
        verify=False,  # skip SSL certificate verification
        follow_redirects=True,
        **kwargs,
    )


async def test_sse_client_connection() -> None:
    """Test SSE client connection and list tools.

    Args:
        config: SSE client configuration
    """
    logging.getLogger("mcp.client").setLevel(logging.DEBUG)

    config = SseClientConfig(
        url="https://star.nezha.inhand.dev/api/v1/nezha-mcp/mcp/?access_token=247b964c3e574e688f7ef63981b151d1",  # replace with your SSE server URL
    )

    exit_stack = AsyncExitStack()

    try:
        logger.info(f"Connecting to SSE endpoint: {config.url}")

        # create SSE client connection with custom httpx client factory that skips SSL verification
        transport = await exit_stack.enter_async_context(
            streamablehttp_client(
                url=config.url,
                headers=config.headers,
                timeout=5,
                sse_read_timeout=5,
                terminate_on_close=False,
                httpx_client_factory=create_insecure_http_client,  # use custom factory to skip SSL verification
            )
        )

        read_stream, write_stream, _ = transport
        logger.info(f"Read stream: {read_stream}")
        logger.info(f"Write stream: {write_stream}")

        # create client session
        session = await exit_stack.enter_async_context(ClientSession(read_stream, write_stream))

        logger.info("Initializing client session...")
        initialization_result = await session.initialize()
        logger.info(f"Session initialized successfully: {initialization_result}")

        # list available tools
        logger.info("Listing available tools...")
        tools_response = await session.list_tools()

        logger.info("Available tools:")
        for item in tools_response:
            if isinstance(item, tuple) and item[0] == "tools":
                tools = item[1]
                if not tools:
                    logger.info("  No tools available")
                else:
                    for tool in tools:
                        logger.info(f"  - {tool.name}: {tool.description}")
                        if hasattr(tool, "inputSchema") and tool.inputSchema:
                            logger.info(f"    Input schema: {json.dumps(tool.inputSchema, indent=4)}")

    except Exception as e:
        logger.error(f"Error occurred: {e}")
        raise
    finally:
        await exit_stack.aclose()


if __name__ == "__main__":
    import asyncio

    asyncio.run(test_sse_client_connection())
