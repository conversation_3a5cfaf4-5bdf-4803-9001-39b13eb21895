import operator
from typing import Annotated, Any

from langfuse import get_client
from langgraph.graph import END, START, StateGraph
from langgraph.types import Send
from loguru import logger
from pydantic import BaseModel, Field

from agent.company_agent import search_company
from agent.contacts_agent import ContactsAgent
from apollo.apollo_api import bulk_people_enrichment
from server.common_types import CustomPrompts
from server.task.base_task_processor import BaseTaskProcessor
from zoho.accounts_api import fetch_accounts_info_by_account_id
from zoho.contacts_api import add_contacts_to_zoho
from zoho.data_transform import safe_str, transform_apollo_contact_data_to_zoho_contact_data
from zoho.get_existing_contacts import get_existing_contacts


# define contact transformation task state
class ContactTransformationTask(BaseModel):
    """single contact transformation task"""

    contact: dict[str, Any] = Field(description="contact data to transform")


# define state for the data processing graph
class DataProcessingState(BaseModel):
    """state for the data processing workflow"""

    # input parameters
    company_name: str = Field(description="company name to process")
    contact_location: str | None = Field(default=None, description="company location")
    organization_website: str | None = Field(default=None, description="company website")
    custom_prompts: CustomPrompts | None = Field(default=None, description="custom filtering prompts")
    account_id: str | None = Field(default=None, description="zoho account id")
    owner_id: str | None = Field(default=None, description="zoho account owner id")

    # intermediate results
    company_info: dict[str, Any] | None = Field(default=None, description="apollo company information")
    exclude_linkedin_urls: list[str] = Field(
        default_factory=list, description="existing contact linkedin urls to exclude"
    )
    potential_contacts: list[dict[str, Any]] = Field(
        default_factory=list, description="potential contacts with high feasibility from agent"
    )
    zoho_contacts: Annotated[list[dict[str, Any]], operator.add] = Field(
        default_factory=list, description="transformed contacts for zoho"
    )


class DataProcessor(BaseTaskProcessor):
    def __init__(self, task_id: str):
        super().__init__(
            task_id=task_id,
            task_name="data_processor_task",
        )
        self.graph = self._build_graph()

    def _build_graph(self):
        """build the data processing workflow graph"""
        builder = StateGraph(DataProcessingState)

        # add nodes for each processing step
        builder.add_node("initial_data_acquisition", self._initial_data_acquisition_node)
        builder.add_node("discover_contacts", self._discover_contacts_node)
        builder.add_node("transform_zoho_format", self._transform_zoho_format_node)
        builder.add_node("sync_to_crm", self._sync_to_crm_node)

        # define the workflow
        builder.add_edge(START, "initial_data_acquisition")
        builder.add_edge("initial_data_acquisition", "discover_contacts")
        builder.add_conditional_edges(
            "discover_contacts", self._check_contacts_found, {"continue": "transform_zoho_format", "empty": END}
        )
        builder.add_edge("transform_zoho_format", "sync_to_crm")
        builder.add_edge("sync_to_crm", END)

        return builder.compile(name="detect_contacts_graph")

    async def _initial_data_acquisition_node(self, state: DataProcessingState) -> DataProcessingState:
        """node: fetch company information and existing contacts"""

        logger.info(
            f"开始处理数据, 公司名称: {state.company_name}, "
            f"公司地址: {state.contact_location}, "
            f"公司网址：{state.organization_website}, "
            f"account_id: {state.account_id}"
        )

        await self.send_thinking_message(f"Searching company information for {state.company_name} on Apollo")

        # search company information
        company_info = await search_company(
            company_name=state.company_name,
            company_location=state.contact_location,
            organization_website=state.organization_website,
        )

        if not company_info:
            raise ValueError(f"Cannot find company information for {state.company_name} on Apollo")

        state.company_info = company_info

        await self.send_thinking_message("Retrieving existing contacts from Zoho")

        # get existing contacts to exclude
        exclude_people_list = get_existing_contacts(state.company_name, state.account_id)
        state.exclude_linkedin_urls = [
            exclude_people["linkedin_url"] for exclude_people in exclude_people_list if exclude_people["linkedin_url"]
        ]

        await self.send_thinking_message(
            f"I've found {len(state.exclude_linkedin_urls)} existing contacts to be excluded from the search result."
        )

        return state

    async def _discover_contacts_node(self, state: DataProcessingState) -> DataProcessingState:
        """node: discover potential contacts using agent and enrich with apollo"""
        # prepare user query
        user_query = "帮我找出目标企业下最合适的联系人。"
        if state.custom_prompts and state.custom_prompts.prompt and state.custom_prompts.prompt != "":
            user_query = state.custom_prompts.prompt

        # filter contacts using agent
        agent = ContactsAgent(exclude_linkedin_urls=state.exclude_linkedin_urls)

        people_list = await agent.get_contacts(user_query=user_query, organization_id=state.company_info["id"])

        # early return if no contacts found
        if not people_list:
            return state

        # enrich contacts with apollo
        await self.send_thinking_message(
            f"I have found {len(people_list)} potential contacts from Apollo. "
            "Next I need to get the basic information for these contacts."
        )

        logger.info(f"正在与 Apollo 进行数据交换: {state.company_name}, 联系人数据: {len(people_list)}条")

        potential_contacts = bulk_people_enrichment(people_list)

        await self.send_thinking_message(
            f"I have retrieved the basic information for these {len(potential_contacts)} contacts."
        )

        state.potential_contacts = potential_contacts or []

        return state

    def _check_contacts_found(self, state: DataProcessingState) -> str | list[Send]:
        """conditional edge: check if any contacts were found"""
        if not state.potential_contacts:
            return "empty"

        # send each contact for parallel transformation
        return [
            Send(
                "transform_zoho_format",
                ContactTransformationTask(contact=contact),
            )
            for contact in state.potential_contacts
        ]

    async def _transform_zoho_format_node(self, task: ContactTransformationTask) -> dict[str, Any]:
        """node: transform single contact to zoho format (runs in parallel)"""
        contact = task.contact

        # clean data inline
        cleaned_contact = {key: safe_str(value) for key, value in contact.items()}
        first_name = cleaned_contact.get("first_name", "")
        last_name = cleaned_contact.get("last_name", "")
        title = cleaned_contact.get("title", "")
        logger.info(f"transforming contact: {first_name} {last_name} - {title}")

        # transform data
        zoho_contact = await transform_apollo_contact_data_to_zoho_contact_data(
            apollo_contact_data=cleaned_contact, default_data={}
        )

        return {"zoho_contacts": [zoho_contact]}

    async def _sync_to_crm_node(self, state: DataProcessingState) -> DataProcessingState:
        """node: sync transformed data to zoho crm"""
        # collect all transformed contacts from parallel processing
        # Note: LangGraph automatically aggregates the results from parallel Send operations
        # The transformed contacts should be available in state at this point

        if not state.zoho_contacts:
            logger.warning("zoho_contacts not found in state, this might indicate an aggregation issue")
            return {}

        zoho_contacts = state.zoho_contacts
        owner_id = state.owner_id  # Use a default owner ID if not provided
        logger.info(f"转换后的联系人数据个数: {len(zoho_contacts)}")

        # add to zoho

        await self.send_thinking_message(f"Adding these {len(zoho_contacts)} contacts to CRM")
        add_contacts_to_zoho(account_id=state.account_id, contacts=zoho_contacts, owner_id=owner_id)
        logger.info(f"数据处理完成: {state.company_name}, 处理结果: {len(zoho_contacts)}条联系人数据")

        return {}

    async def _arun(self, data: dict[str, Any]) -> dict[str, Any]:
        """
        Main entry point for processing. Fetches initial data and then calls core processing logic.
        Returns the processing result dictionary or raises an exception.
        Args:
            data (Dict[str, Any]): The data to process, expected to contain company_info with account_id.
        """
        account_id = data.get("account_id")
        owner_id = data.get("owner_id")
        if not account_id:
            raise ValueError("missing account_id in input data")

        langfuse = get_client()
        langfuse.update_current_trace(
            user_id=data.get("current_user", {}).get("email"),
            metadata={
                "account_id": account_id,
            },
        )

        zoho_account_info = await fetch_accounts_info_by_account_id(account_id)

        if not zoho_account_info:
            raise ValueError(f"cannot find zoho account info for account_id: {account_id}")

        company_name = zoho_account_info.get("name")
        contact_location = zoho_account_info.get("territory")
        organization_website = zoho_account_info.get("website")
        custom_prompts_data = data.get("custom_prompts")
        # Ensure the CustomPrompts type is handled if not None
        custom_prompts = CustomPrompts(**custom_prompts_data) if custom_prompts_data else None

        if not company_name:
            raise ValueError(f"missing company name in zoho account info for account_id: {account_id}")

        # create initial state
        initial_state = DataProcessingState(
            company_name=company_name,
            contact_location=contact_location,
            organization_website=organization_website,
            custom_prompts=custom_prompts,
            account_id=account_id,
            owner_id=owner_id,
        )

        # execute the graph
        final_state = await self.graph.ainvoke(initial_state)

        contacts = final_state.get("zoho_contacts", []) if isinstance(final_state, dict) else final_state.zoho_contacts

        return {"data": contacts, "total": len(contacts), "content": f"Added these {len(contacts)} contacts to CRM"}
