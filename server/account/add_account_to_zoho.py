from zoho.accounts_api import add_account_to_zoho, get_owner_info_by_user_id


async def add_account_to_crm_zoho(account_info: dict, user_id: str) -> str:
    """
    添加公司到crm zoho

    Args:
        account_info: 公司信息
        user_id: 当前用户ID

    Returns:
        account_id: 添加成功后的公司ID
    """
    if not account_info or not user_id:
        raise Exception("account_info or user_id is required")

    owner_info = await get_owner_info_by_user_id(user_id)
    if not owner_info:
        raise Exception("Failed to get owner info")

    zoho_account_info = {
        "name": account_info.get("name", "-"),
        "website": account_info.get("website", ""),
        "owner": {
            "id": owner_info.get("id", ""),
        },
        "account_Type": account_info.get("account_type", "-None-"),
        "industry": account_info.get("industry", "-None-"),
        "market_segments": account_info.get("market_segments", ["DA"]),
        "territory": account_info.get("territory", "-None-"),
        "billing_state": account_info.get("address_state", "Unknown"),
        "stage": "Prospect",
        "priority": "Low",
        "lead_source": "AI Research",
        "labels": "sales_agent",
    }

    result = await add_account_to_zoho(zoho_account_info)
    return result["id"]


if __name__ == "__main__":
    user_id = "*********"
    account_info = {
        "name": "Atlas Copco",
        "website": "https://www.atlascopco.com",
        "account_type": "OEM",
        "industry": "Industry",
        "market_segments": ["Automation", "IoT", "Energy Mgt."],
        "territory": "Sweden",
        "address_state": "Stockholm",
    }
    import asyncio

    result = asyncio.run(add_account_to_crm_zoho(account_info, user_id))
    print(result)
