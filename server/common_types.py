"""
公共类型和枚举定义模块
用于避免循环导入
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, TypedDict

from pydantic import BaseModel, Field
from taskiq import TaskiqResult
from typing_extensions import NotRequired


class TaskType(str, Enum):
    """任务类型枚举"""

    DETECT_CONTACTS = "sales_agent"  # 探查联系人
    PROSPECTING_ACCOUNTS = "prospecting_accounts"  # 探查潜在客户


class TaskStatus(str, Enum):
    """任务状态枚举"""

    PENDING = "pending"  # 等待执行
    RUNNING = "running"  # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"  # 执行失败
    CANCELED = "canceled"  # 被取消
    SUCCESS = "success"  # 成功


class MessageType(str, Enum):
    THINKING = "thinking"  # 思考中
    START = "start"  # 开始
    TOTAL = "total"  # 总人数
    LATENT = "latent"  # 潜在联系人
    FILTER_LATENT = "filter_latent"  # 过滤潜在联系人
    GET_CONTACTS_INFO = "get_contacts_info"  # 获取联系人信息
    UPDATE_CONTACTS_INFO = "update_contacts_info"  # 更新联系人信息
    GET_COMPLETED_CONTACTS_DATA = "get_completed_contacts_data"  # 获取完成的数据
    READY_DATA_TRANSFORM = "ready_data_transform"  # 准备转换数据格式
    DATA_TRANSFORM = "data_transform"  # 数据转换
    ADD_CONTACTS = "add_contacts"  # 添加联系人
    COMPLETED = "completed"  # 完成
    SUCCESS = "success"  # 成功
    ERROR = "error"  # 错误


class CustomPrompts(BaseModel):
    """自定义提示词模型"""

    prompt: str = Field(..., description="提示内容")
    cover_system_prompt: bool = False


class CurrentUser(BaseModel):
    """当前用户信息模型"""

    email: Optional[str] = Field(None, description="用户邮箱")
    username: Optional[str] = Field(None, description="用户名")


class CompanyInfo(BaseModel):
    """公司信息模型"""

    account_id: str = Field(..., description="账户ID")
    owner_id: Optional[str] = Field(None, description="所有者 ID")
    custom_prompts: Optional[CustomPrompts] = None
    current_user: Optional[CurrentUser] = None


class BatchAccountTaskRequest(BaseModel):
    """批量账户任务请求模型"""

    account_ids: List[str] = Field(..., description="账户ID列表", min_length=1)


class ResponseData(TypedDict):
    type: MessageType
    total: NotRequired[int]
    data: NotRequired[List[Dict[str, Any]]]
    message: str


class ContactEnrichmentStatus(TypedDict):
    contact_id: str
    request_id: str
    status: str  # pending, success, failed
    updated_at: str


class StreamMessageType(str, Enum):
    """Stream message type enum"""

    THINKING = "thinking"  # Reasoning chunk
    TEXT = "text"  # Output text
    DATA = "data"  # Data object

    TOOL_CALL = "tool_call"  # Tool call
    TOOL_RESULT = "tool_result"  # Tool result

    STEP_START = "step_start"  # Step start
    STEP_END = "step_end"  # Step end

    FINISH = "finish"  # Finish
    ERROR = "error"  # Error


class StreamMessage(BaseModel):
    """Stream message model"""

    type: StreamMessageType
    content: Optional[str] = Field(None, description="content of the message")
    data: Optional[dict[str, Any]] = Field(None, description="data object for data message")

    tool_call_id: Optional[str] = Field(None, description="tool call id for tool_call message")
    tool_name: Optional[str] = Field(None, description="tool name for tool_call message")
    tool_input: Optional[dict[str, Any]] = Field(None, description="tool input for tool_call message")
    tool_result: Optional[Any] = Field(None, description="tool result for tool_result message")

    error: Optional[str] = Field(None, description="error for finish message")
    result: Optional[Any] = Field(None, description="result for finish message")

    timestamp: str = Field(
        default_factory=lambda: datetime.now(timezone.utc).isoformat(), description="timestamp for message"
    )

    tags: Optional[list[str]] = Field(None, description="tags for message")
    metadata: Optional[dict[str, Any]] = Field(None, description="metadata for message")

    @classmethod
    def from_exception(cls, exception: Exception) -> "StreamMessage":
        return cls(type=StreamMessageType.ERROR, content=str(exception))

    @classmethod
    def from_task_result(cls, result: TaskiqResult[Any]) -> "StreamMessage":
        return cls(
            type=StreamMessageType.FINISH, result=result.return_value, error=str(result.error) if result.error else None
        )

    @classmethod
    def from_thinking(cls, content: str) -> "StreamMessage":
        return cls(type=StreamMessageType.THINKING, content=content)

    @classmethod
    def from_text(cls, content: str) -> "StreamMessage":
        return cls(type=StreamMessageType.TEXT, content=content)

    @classmethod
    def from_data(cls, data: dict[str, Any]) -> "StreamMessage":
        return cls(type=StreamMessageType.DATA, data=data)

    @classmethod
    def from_tool_call(cls, tool_call_id: str, tool_name: str, tool_input: dict[str, Any]) -> "StreamMessage":
        return cls(
            type=StreamMessageType.TOOL_CALL, tool_call_id=tool_call_id, tool_name=tool_name, tool_input=tool_input
        )

    @classmethod
    def from_tool_result(cls, tool_call_id: str, tool_name: str, tool_result: Any) -> "StreamMessage":
        return cls(
            type=StreamMessageType.TOOL_RESULT,
            tool_result=tool_result,
            tool_call_id=tool_call_id,
            tool_name=tool_name,
        )

    @classmethod
    def from_step_start(cls, step_name: str) -> "StreamMessage":
        return cls(type=StreamMessageType.STEP_START, step_name=step_name)

    @classmethod
    def from_step_end(cls, step_name: str, result: Any) -> "StreamMessage":
        return cls(type=StreamMessageType.STEP_END, step_name=step_name, result=result)
