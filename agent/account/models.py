from typing import List, Optional

from pydantic import BaseModel, Field


class CompanyBasicInfo(BaseModel):
    """单个公司基础信息"""

    id: Optional[str] = Field(default=None, description="公司ID")
    name: str = Field(default=None, description="公司名称")
    website_url: Optional[str] = Field(default=None, description="公司网站")
    linkedin_url: Optional[str] = Field(default=None, description="LinkedIn链接")
    organization_revenue: Optional[str] = Field(default=None, description="公司营收")
    phone: Optional[str] = Field(default=None, description="联系电话")
    founded_year: Optional[int] = Field(default=None, description="成立年份")


class SimilarCompaniesResult(BaseModel):
    """相似公司搜索结果"""

    title: str = Field(default="", description="为本次调研任务总结一个非常简短的标题")
    companies: List[CompanyBasicInfo] = Field(default_factory=list, description="相似公司列表")
    error: Optional[str] = Field(default=None, description="错误信息")
