from typing import Optional, TypedDict

from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate

from agent.account.analyzer.explanation_user_input import explanation_user_input
from agent.tools import (
    firecrawl_scrape,
    tavily_crawl,
    tavily_search,
)
from agent.tools.linkedin import get_company_details
from utils import init_model
from utils.file_handler import load_file
from zoho.accounts_api import ZohoAccountInfo
from langgraph.graph import END, START, StateGraph
from langchain_core.callbacks import dispatch_custom_event


class AccountAnalyzeState(TypedDict):
    """State for the analysis workflow."""

    # input
    account_info: ZohoAccountInfo
    user_input: Optional[str]  # 用户输入的分析需求

    # state
    explanation_user_input: str  # 分析目标
    analyze_type: str

    # result
    analysis_report: str  # 分析报告内容


class AccountAnalyzeGraph:
    def __init__(self):
        self.graph = self._build_graph()

    def _build_graph(self):
        builder = StateGraph(AccountAnalyzeState)

        # 1. 分析用户输入的意图
        # 2. 获取目标公司信息
        # 3. 综合获取到的信息，进行分析

        # add nodes
        builder.add_node("analyze_user_input", self._explanation_user_input)

        # add edges
        builder.add_edge(START, "explanation_user_input")
        builder.add_edge("explanation_user_input", END)

        return builder.compile()

    async def _explanation_user_input(self, state: AccountAnalyzeState) -> AccountAnalyzeState:
        dispatch_custom_event("thinking", f"正在解释用户输入: {state['user_input']}")
        if not state["user_input"]:
            raise Exception("user_input is None")
        explanation = await explanation_user_input(state["user_input"])
        state["explanation_user_input"] = explanation
        return state


async def analyze_account_workflow(account_info: ZohoAccountInfo, user_input: Optional[str] = None) -> str:
    """
    Analyze the account info and return a markdown formatted analysis report.

    Args:
        account_info: ZohoAccountInfo object containing company information
        analyze_type: "reseller" or "direct_customer"
        user_input: User's specific analysis requirements and focus areas

    Returns:
        str: Markdown-formatted analysis report
    """
    # create llm agent to analyze the account info

    tools = [tavily_search, tavily_crawl, firecrawl_scrape, get_company_details]

    llm = init_model(
        model="gemini-2.5-pro",
        max_tokens=10240,
        temperature=0.1,
        thinking_budget=256,
        include_thoughts=True,
    )

    llm.bind_tools(tools)

    # 加载分析 prompt
    system_prompt = load_file("agent/account/analyzer/prompts/analysis_company.md")

    # 加载映翰通公司简介
    inhand_business_card = load_file("prompts/inhand_business_card.md")

    if not system_prompt:
        raise ValueError("system_prompt is None")

    if not inhand_business_card:
        raise ValueError("inhand_business_card is None")

    # 将映翰通简介插入到系统提示词中
    system_prompt = system_prompt.format(inhand_business_card=inhand_business_card)

    # 构建提示词
    prompt = ChatPromptTemplate.from_messages(
        [
            SystemMessagePromptTemplate.from_template(system_prompt),
            HumanMessagePromptTemplate.from_template("账户信息: {account_info}\n用户需求: {user_input}"),
        ]
    )

    chain = prompt | llm

    result = await chain.ainvoke(
        {
            "account_info": account_info,
            "user_input": user_input or "请按照标准流程进行全面分析",
        }
    )

    # 直接提取并返回文本内容
    if hasattr(result, "content"):
        if isinstance(result.content, list):
            # 过滤掉 thinking 类型的内容，只保留文本内容
            text_content = []
            for item in result.content:
                if isinstance(item, dict) and item.get("type") == "text":
                    text_content.append(item.get("text", ""))
                elif isinstance(item, str):
                    text_content.append(item)
            return "\n".join(text_content)
        else:
            return str(result.content)
    else:
        return str(result)


if __name__ == "__main__":
    import asyncio
    from dotenv import load_dotenv
    from loguru import logger
    from zoho.accounts_api import fetch_accounts_info_by_account_id

    load_dotenv()

    async def direct_customer_analyze():
        # 分析 Sullair Australia (3091799000303143016) 使用我们工业路由器或者边缘计算网关的潜力
        user_input = "请帮我分析 Sullair Australia 使用我们工业路由器或者边缘计算网关的潜力"
        account_info = await fetch_accounts_info_by_account_id("3091799000303143016")
        if not account_info:
            logger.error("account_info is None")
            return
        logger.info("Sullair Australia 账户信息: {}", account_info)
        result = await analyze_account_workflow(account_info=account_info, user_input=user_input)
        logger.info("Sullair Australia 成为直接客户的潜力分析结果: {}", result)

    async def comprehensive_analyze():
        # 分析与 iiNet (3091799000289213015) 在企业网络上的合作
        user_input = "请帮我分析与 iiNet 在企业网络上的合作"
        account_info = await fetch_accounts_info_by_account_id("3091799000289213015")
        if not account_info:
            logger.error("account_info is None")
            return
        logger.info("iiNet 账户信息: {}", account_info)
        result = await analyze_account_workflow(account_info=account_info, user_input=user_input)
        logger.info("iiNet 的业务合作关系分析结果: {}", result)

    if __name__ == "__main__":
        # asyncio.run(reseller_analyze())
        # asyncio.run(direct_customer_analyze())
        asyncio.run(comprehensive_analyze())
