from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate
from loguru import logger

from utils import init_model
from utils.file_handler import load_file


async def explanation_user_input(user_input: str) -> str:
    """
    解释用户输入
    """
    llm = init_model(
        model="gpt-4.1-mini",
        max_tokens=1024,
        temperature=0.1,
    )
    # load system prompt
    system_prompt = load_file("agent/account/analyzer/prompts/explanation_user_input.md")
    # load business card
    inhand_business_card = load_file("prompts/inhand_business_card.md")

    if not system_prompt:
        raise ValueError("system prompt not found")

    if not inhand_business_card:
        raise ValueError("inhand business card not found")

    # format system prompt
    system_prompt = system_prompt.format(inhand_business_card=inhand_business_card)

    # prompt
    prompt = ChatPromptTemplate.from_messages(
        [
            SystemMessagePromptTemplate.from_template(system_prompt),
            HumanMessagePromptTemplate.from_template(user_input),
        ]
    )

    chain = prompt | llm

    result = await chain.ainvoke({"user_input": user_input})

    # BaseMessage does not have .get(), use .content if available
    content = getattr(result, "content", "")
    return content


if __name__ == "__main__":
    import asyncio

    async def main():
        user_input = "请帮我分析与 iiNet 在企业网络上的合作"
        result = await explanation_user_input(user_input=user_input)
        logger.info(result)

    asyncio.run(main())
