import asyncio
from typing import List, Optional

from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, <PERSON>

from config import MODEL
from utils import init_model, load_env
from utils.file_handler import load_file
from utils.logger import get_logger

logging = get_logger(__name__)


class AccountInfo(BaseModel):
    """公司信息结构"""

    name: str = Field(description="公司名称")
    website: str = Field(description="公司官网")
    account_type: str = Field(
        description="公司类型，必须是以下选项之一：['-None-','EU(End User)','OEM','SI(System Integrator)','DIS(Distributor)','Operator(Fleet or Vending Operator)','Sales Rep','Telecom Operator','Partner','IoT Platform','MSP','VAR Reseller','Others']"  # noqa: E501
    )
    industry: str = Field(
        description="行业，必须是以下选项之一：['-None-','Energy','Industry','Commerce','ICT','Mobility','City','Others']"
    )
    market_segments: List[str] = Field(
        description="行业细分领域，必须是以下选项（可多选）：['DA','Energy Mgt.','Renewable','Metering','Automation','Building','RTM','ATM','POS','Kiosk','Digital Signage','Vending','Business Networking','Telecom','IoT','IT(General IT)','Fleet','Cole Chain','Traffic','Transportation','Railway','Parking','EB Charging','Lighting','Security','Healthcare','Education','Agriculture','Government']"  # noqa: E501
    )
    territory: str = Field(description="公司总部地址（只填国家）")
    address_state: str = Field(description="公司总部地址（省/州）")


class CompanyResearchData(BaseModel):
    """单个公司调研报告数据结构"""

    content: str = Field(description="相似公司分析内容（Markdown格式）")
    account_info: AccountInfo = Field(description="公司信息")


class ExtractResearchResult(BaseModel):
    """提取调研结果的结构化输出"""

    data: List[CompanyResearchData] = Field(default_factory=list, description="相似公司列表")
    error: Optional[str] = Field(default=None, description="错误信息（必须使用英文，若没有错误信息则不显示此字段）")


async def extract_research_results(research_results: list[str]) -> ExtractResearchResult:
    """结构化提取多个公司的调研结果"""
    if len(research_results) == 0:
        raise Exception("research results is empty")

    logging.debug("正在提取调研结果...")

    llm = init_model(model=MODEL, max_tokens=10240, temperature=0.1)
    structured_llm = llm.with_structured_output(schema=ExtractResearchResult)

    system_prompt = load_file("agent/account/prompts/extract_research_result.md")
    user_prompt = "请帮我提取出调研结果中的结构化信息：\n{research_result}"

    prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("human", user_prompt)])
    chain = prompt | structured_llm

    result: ExtractResearchResult = await chain.ainvoke({"research_result": "\n\n".join(research_results)})

    if result.error:
        raise Exception(result.error)
    if len(result.data) == 0:
        raise Exception("extract research result is empty")

    logging.debug("提取调研结果完成")
    return result


if __name__ == "__main__":
    load_env()

    research_results = [
        "## Tulip Interfaces\n\n### 基础信息\n- **公司官网:** [https://tulip.co](https://tulip.co)\n- **公司地址:** 77 Middlesex Avenue, Somerville, Massachusetts, 02145, US [[1]](https://www.linkedin.com/company/tulip-interfaces)\n- **员工规模:** 338人 [[1]](https://www.linkedin.com/company/tulip-interfaces)\n- **年收入:** 约 $65.1 百万美元 [[2]](https://rocketreach.co/tulip-interfaces-profile_b5b5c067f64ea5a1)\n- **成立时间:** 2012年 [[1]](https://www.linkedin.com/company/tulip-interfaces)\n\n### 业务分析\n- **行业领域:** 软件开发、工业物联网 (IIoT) [[1]](https://www.linkedin.com/company/tulip-interfaces)\n- **主营业务/产品:** Tulip 提供一个领先的前线运营平台 (Frontline Operations Platform)，这是一个无代码平台，旨在帮助制造、制药和医疗设备等行业的公司创建应用程序来管理其车间运营。该平台能够实现流程的数字化转型、指导操作员、跟踪生产并提供实时的运营全景视图。 [[1]](https://www.linkedin.com/company/tulip-interfaces)\n- **与我们合作相关的业务/产品:** 公司的核心业务与工业物联网（IIoT）原生集成，通过连接和整合现有的系统、机器和设备来采集数据。为了实现这一点，Tulip 平台依赖于边缘硬件设备来连接物理世界的机器、传感器和工具，并采集实时数据。[[3]](https://support.tulip.co/docs/tulip-and-iot-guide)\n\n### 合作机会\n- **技术应用现状:** Tulip 平台通过云端编辑器创建和管理应用，并通过边缘连接的硬件设备进行部署。值得注意的是，Tulip 已经开发并销售自有的边缘硬件设备，名为 **Edge IO** 和 **Edge MC**。这些设备可以直接与平台中的触发器（Triggers）轻松集成。同时，Tulip 的官方文档也明确指出，其平台**可以支持其他第三方边缘设备**。[[3]](https://support.tulip.co/docs/tulip-and-iot-guide)\n- **产品需求评估:** Tulip 的整个商业模式都建立在将车间数据成功采集并传输到其软件平台的基础上。因此，他们对稳定、可靠且易于集成的边缘网关产品有持续且强烈的需求。虽然他们有自己的硬件产品（Edge IO, Edge MC），但在面对客户多样化的连接需求、特定的工业环境或成本敏感型项目时，他们或其系统集成商合作伙伴很可能需要更丰富、更具性价比或功能更特定的第三方边缘硬件作为补充或替代方案。\n- **现有合作伙伴/供应商:**\n    - **技术合作伙伴:** Tulip 与 Microsoft 和 AWS 等云解决方案巨头建立了重要的技术合作关系。[[4]](https://canvasbusinessmodel.com/products/tulip-interfaces-porters-five-forces)\n    - **生态系统合作伙伴:** 他们拥有一个由硬件、软件和云服务商组成的合作伙伴网络，共同为客户创建解决方案。此外，他们还依赖一个由经销商和系统集成商组成的网络来帮助客户构建、实施和部署 Tulip 解决方案。[[5]](https://tulip.co/partners/)\n- **合作可能性评估:** 合作可能性**高**。\n尽管 Tulip 有自己的边缘硬件，但这并不构成合作的障碍，反而证明了其业务对边缘网关的依赖性。他们开放支持第三方设备的策略是关键的合作入口。我们的产品可以作为其自有硬件的补充，为客户提供更多选择，特别是在性能、接口、认证或成本方面有特殊要求的场景。此外，与他们的系统集成商合作，将我们的网关产品作为推荐硬件之一，也是一个极具潜力的合作模式。\n- **合作机会点:**\n    1.  **成为官方认证的第三方硬件供应商:** 与 Tulip 合作，将我们的边缘网关产品列入其官方支持的第三方硬件列表，为 Tulip 的客户和集成商提供一个经过验证的、可靠的替代选择。\n    2.  **提供功能互补的产品:** 针对 Tulip 自有硬件（Edge IO/MC）功能覆盖不到的特定场景（例如需要更多串口/IO接口、需要特定工业协议转换、或对网络安全有更高要求的应用），提供差异化的网关产品。\n    3.  **开拓其系统集成商网络:** 直接与 Tulip 的系统集成商和经销商合作。这些集成商在为最终客户设计方案时，通常有更大的灵活性和决策权来选择最合适的硬件，我们可以将产品销售给他们，作为 Tulip 项目的一部分。\n    4.  **联合解决方案开发:** 针对特定行业（如制药、汽车、电子）的痛点，与 Tulip 合作开发捆绑我们硬件和他们软件的联合解决方案，进行共同市场推广。",  # noqa: E501
        "## Augury\n\n### 基础信息\n- **公司官网:** [https://www.augury.com](https://www.augury.com)\n- **公司地址:** 469 Fashion Ave, New York, NY 10018, US [[2]](https://www.linkedin.com/company/augury-systems)\n- **员工规模:** 384人 (领英数据显示) [[2]](https://www.linkedin.com/company/augury-systems)\n- **成立时间:** 2011年 [[2]](https://www.linkedin.com/company/augury-systems)\n- **年收入:** 约6800万美元 [[3]](https://compworth.com/company/augury)\n\n### 业务分析\n- **行业领域:** 工业物联网 (IIoT)、软件开发、人工智能 [[2]](https://www.linkedin.com/company/augury-systems)\n- **主营业务/产品:** Augury 提供以人工智能为核心的机器健康 (Machine Health) 和过程健康 (Process Health) 解决方案。其平台通过结合物联网传感器、AI算法和专家诊断，帮助制造、消费品、制药、能源等行业的客户预测和预防工业机械故障，从而减少停机时间、提高生产效率和实现可持续发展目标。[[1]](https://endpoint.augury.com/en/kb/articles/17-introduction-to-augury)\n- **与我们合作相关的业务/产品:** Augury 的核心业务依赖于从客户的工业设备上采集关键数据（如振动、温度、磁场数据）。其全栈式解决方案包括了从数据采集硬件（传感器）到云端AI分析的整个流程。[[5]](https://endpoint.augury.com/en/kb/articles/17-introduction-to-augury) 他们于2024年11月发布了名为 Halo™ R4000 系列的新一代传感器，并称之为“工业级、边缘AI原生机器健康传感平台”，这表明他们正在将AI能力下沉到边缘端。[[4]](https://www.augury.com/media-center/press/augury-introduces-the-first-industrial-grade-edge-ai-native-machine-health-sensing-platform/)\n\n### 合作机会\n- **技术应用现状:** Augury的技术栈结合了自研的传感器硬件、云平台和AI算法。他们通过部署在机器上的传感器实时捕捉振动、温度和磁场数据，然后利用机器学习算法进行分析，为客户提供预测性维护建议。[[6]](https://cloud.google.com/customers/augury/) 其最新推出的Halo R4000系列传感器具备边缘AI能力，可以在设备端进行初步的智能诊断。[[4]](https://www.augury.com/media-center/press/augury-introduces-the-first-industrial-grade-edge-ai-native-machine-health-sensing-platform/)\n- **产品需求评估:** Augury的商业模式决定了它必须拥有一个稳定、可靠的数据采集和传输层。虽然他们已经推出了具备边缘AI能力的传感器，但这通常只解决了数据采集和初步处理的问题。在复杂的工业环境中，将大量传感器的数据进行汇聚、预处理，并安全可靠地传输到云端，正是边缘计算网关的核心价值所在。Augury的解决方案需要处理来自众多传感器的数据，一个强大的边缘网关可以：\n    1.  **数据聚合与缓冲：** 汇聚多个传感器的数据，减少网络连接点。\n    2.  **协议转换：** 兼容工厂中可能存在的多种工业协议。\n    3.  **边缘计算能力：** 运行更复杂的AI模型，分担其云端平台的计算压力，并实现更快的本地响应。\n    4.  **网络管理与安全：** 提供稳定的蜂窝网络或Wi-Fi连接，并确保数据传输的安全性。\n- **现有合作伙伴/供应商:**\n    - **技术与商业联盟:** Augury与**贝克休斯 (Baker Hughes)** 建立了重要的商业联盟，将其技术与Bently Nevada的System 1平台进行整合。[[7]](https://www.bakerhughes.com/company/energy-forward/sounds-science-revolution-machine-health)\n    - **物流与服务:** 与全球物流供应商 **DSV** 合作，推出“备件即服务”(Parts-as-a-Service)解决方案。[[8]](https://www.businesswire.com/news/home/<USER>/en/Augury-and-DSV-Announce-Groundbreaking-Partnership-to-Introduce-New-Parts-As-a-Service-PaaS-Solution-for-Industrial-Manufacturing)\n    - **云服务:** 使用 **Google Cloud** 作为其云基础设施。[[6]](https://cloud.google.com/customers/augury/)\n    - 目前的公开资料中**未明确提及**其边缘网关或类似网络设备的供应商。这可能意味着他们自研了部分功能，或者这是一个潜在的合作缺口。\n- **合作可能性评估:** **高**。Augury的业务核心是AI分析服务，而非硬件制造。虽然他们设计自己的传感器，但在更通用的边缘计算和网络连接层面，与专业的硬件供应商合作可以使其更专注于自身的核心算法和软件平台，同时降低硬件开发和维护成本。我们的边缘网关产品可以作为其解决方案中关键的基础设施组件，帮助他们更好地扩展和部署服务。\n- **合作机会点:**\n    1.  **作为其解决方案的捆绑硬件：** 将我们的边缘网关作为Augury“全栈解决方案”的一部分，为其客户提供稳定可靠的数据聚合与传输能力。\n    2.  **增强其边缘计算能力：** 我们的边缘计算网关可以提供比其传感器内置芯片更强大的计算能力，允许Augury在边缘端部署更复杂的AI模型，实现更高级的本地诊断和实时警报，减少对云端的依赖。\n    3.  **解决复杂网络环境连接问题：** 在一些大型工厂或网络环境复杂的场景下，我们的工业级网关可以提供更稳定、安全的网络连接方案（如有线、Wi-Fi、4G/5G蜂窝网络），确保其传感器数据能够可靠上传。\n    4.  **开拓新市场：** 针对一些需要处理大量设备、网络条件不佳或对数据本地化处理有更高要求的客户，我们的边缘网关可以帮助Augury满足这些客户的特定需求，共同开拓新市场。",  # noqa: E501
    ]
    result: ExtractResearchResult = asyncio.run(extract_research_results(research_results))
    print(result.model_dump_json())
