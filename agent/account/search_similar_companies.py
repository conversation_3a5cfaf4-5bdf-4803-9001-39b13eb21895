import asyncio
import json
from typing import List, Optional

import yaml
from langchain_core.prompts import ChatPromptTemplate
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.account.models import SimilarCompaniesResult
from agent.tools.apollo import search_organizations
from utils.file_handler import load_file
from utils.models import init_model


async def search_similar_companies(
    user_query: str,
    template_company_with_detail: str,
    excludes: Optional[List[str]],
    limit: Optional[int] = 5,
) -> SimilarCompaniesResult:
    """
    根据用户查询需求和模板公司详细信息，搜索相似公司列表，并返回筛选后的相似公司列表

    Args:
        user_query: 用户查询
        template_company_with_detail: 模板公司详细信息
        excludes: 排除的公司列表
        limit: 限制返回公司的数量，默认5

    Returns:
        SimilarCompaniesResult: 筛选后的相似公司列表
    """
    logger.info("开始搜索相似公司列表...")

    llm = init_model(model="gemini-2.5-pro", max_tokens=8192, temperature=0.1, include_thoughts=False)
    tools = [search_organizations]

    system_prompt = load_file("agent/account/prompts/search_similar_companies.md")
    if not system_prompt:
        raise ValueError("无法加载系统提示文件")

    prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")])

    # 创建 React Agent 并指定 response_format
    agent = create_react_agent(
        model=llm,
        tools=tools,
        prompt=prompt,
        response_format=SimilarCompaniesResult,
    )

    # 输入消息，使用简化的模板公司信息
    input_message = (
        f"用户查询需求：\n{user_query}\n\n模板公司信息：\n{template_company_with_detail}\n\n限制返回的公司数量：{limit}"
    )
    if excludes and len(excludes) > 0:
        input_message += f"\n\n需要排除的公司列表：\n{json.dumps(excludes)}"

    result = await agent.ainvoke({"messages": [("human", input_message)]})

    # 获取结构化输出
    if result.get("structured_response"):
        structured_result: SimilarCompaniesResult = result.get("structured_response")
        logger.info(f"搜索相似公司列表完成，获取到 {len(structured_result.companies)} 个候选公司")
    else:
        raise Exception("no valid search result")

    # 校验结果
    if structured_result.error:
        raise Exception(structured_result.error)
    if len(structured_result.companies) == 0:
        raise Exception("no similar companies found")

    return structured_result


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()

    async def main():
        # 测试数据
        user_query = "我想找一些像Cirrus Link Solutions这样的需要使用边缘网关产品的Industrial IoT公司，我想把我们的边缘网关产品卖给他们"  # noqa: E501
        template_company_with_detail = yaml.dump(
            {
                "name": "iiNet",
                "website": "http://www.iinet.net.au/",
                "description": "iiNet is an industry-leading Australian internet provider with services available nationwide. With our wide range of NBN plans and our own ULTRA Broadband Cable, FTTB & VDSL2 networks, we make it easy for Aussies to connect to great-value broadband.",  # noqa: E501
                "territory": "Australia",
                "industry": "ICT",
                "market_segments": ["DA"],
                "detail": "- description: \"iiNet is an Australian Internet Service Provider and a leading challenger\\\n    \\ in the telecommunications market. We're committed to making it simple for all\\\n    \\ Australians to get online across both our own ADSL2+ network and the exciting\\\n    \\ new nbn\\u2122 technologies. \\n\\nOur vision is to lead the market with services\\\n    \\ that harness the potential of the Internet and then differentiate with award-winning\\\n    \\ customer service. We employ more than 2,500 enthusiastic staff across four countries\\\n    \\ - 80 per cent of whom are employed to directly service nearly one million customers.\\n\\\n    \\niiNet has a proud history of delivering awesome customer service, which has\\\n    \\ won us dozens of awards, including a silver medal in Customer Service at the\\\n    \\ 2015 Contact Centre World Best Practice Conference (Asia-Pacific).\\n\\nA lot\\\n    \\ has changed since iiNet was founded in a suburban garage in 1993 and the broadband\\\n    \\ landscape continues to evolve. What hasn't changed is our passion for the transformative\\\n    \\ benefits of the Internet and our commitment to helping Australians connect better.\\n\"\n  follower_count: 17067\n  founded: null\n  headquarters:\n    city: Perth\n    country: AU\n    geographic_area: Western Australia\n    line1: Ground Floor, 502 Hay Street, Subiaco\n    postal_code: '6008'\n  id: '13426'\n  industries:\n  - Telecommunications\n  linkedin_url: https://www.linkedin.com/company/iinet\n  name: iiNet\n  specialities:\n  - internet access\n  - VoIP\n  - adsl2+\n  - nbn\n  - Cable\n  - VDSL2\n  - Mobile\n  staff_count: 871\n  staff_count_range: 1001 - 5000\n  tagline: ''\n  type: Public Company\n  universal_name: iinet\n  website: http://www.iinet.net.au\n",  # noqa: E501
            }
        )
        excludes = ["Inhand Networks", "Ingersoll Rand", "Cirrus Link Solutions"]

        result: SimilarCompaniesResult = await search_similar_companies(
            user_query=user_query,
            template_company_with_detail=template_company_with_detail,
            excludes=excludes,
        )
        logger.info(f"search_similar_companies result: {result.model_dump_json()}")

    asyncio.run(main())
