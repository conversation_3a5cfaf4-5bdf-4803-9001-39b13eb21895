import asyncio
import json
from typing import List, NotRequired, Optional, TypedDict

import yaml
from langchain.callbacks.base import BaseCallbackHandler
from langchain_core.callbacks import dispatch_custom_event
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.graph.state import CompiledStateGraph
from loguru import logger

from agent.account.extract_research_result import ExtractResearchResult, extract_research_results
from agent.account.models import (
    CompanyBasicInfo,
    SimilarCompaniesResult,
)
from agent.account.research_company import research_company
from agent.account.search_similar_companies import search_similar_companies
from agent.tools import get_company_details


class ResearchState(TypedDict):
    """调研状态数据结构"""

    # 输入信息
    user_query: str  # 用户查询需求
    company_info: dict  # 模板公司基本信息
    excludes: NotRequired[List[str]]  # 排除的公司列表

    # 业务数据
    template_company_with_detail: str  # 模板公司详细
    similar_companies: NotRequired[List[CompanyBasicInfo]]  # 相似公司列表
    research_results: NotRequired[List[str]]  # 调研结果

    # 结果数据
    title: NotRequired[str]  # 任务标题
    extract_result: NotRequired[ExtractResearchResult]  # 提取的结构化结果


class ResearchStateGraph:
    """调研工作流状态图"""

    def __init__(self, callbacks: Optional[list[BaseCallbackHandler]] = None, config: Optional[RunnableConfig] = None):
        self.callbacks = callbacks
        self.config = config
        self.graph = self._create_graph()

    def _create_graph(self) -> CompiledStateGraph:
        """创建状态图"""
        workflow = StateGraph(ResearchState)

        # 添加业务节点
        workflow.add_node("get_template_company_info_node", self._get_template_company_info_node)
        workflow.add_node("search_similar_companies", self._search_similar_companies_node)
        workflow.add_node("research_companies", self._research_companies_node)
        workflow.add_node("extract_results", self._extract_results_node)

        # 设置入口点
        workflow.set_entry_point("get_template_company_info_node")

        # 设置边
        workflow.add_edge("get_template_company_info_node", "search_similar_companies")
        workflow.add_edge("search_similar_companies", "research_companies")
        workflow.add_edge("research_companies", "extract_results")
        workflow.add_edge("extract_results", END)

        graph = workflow.compile()

        # 处理配置 - 合并 callbacks 和自定义 config
        final_config = None
        if self.callbacks and len(self.callbacks) > 0:
            final_config = RunnableConfig(callbacks=self.callbacks)

        if self.config:
            if final_config:
                # 合并配置
                final_config = RunnableConfig(
                    callbacks=final_config.get("callbacks", []),
                    metadata={**(final_config.get("metadata", {})), **(self.config.get("metadata", {}))},
                )
            else:
                final_config = self.config

        if final_config:
            graph = graph.with_config(config=final_config)

        return graph

    async def _get_template_company_info_node(self, state: ResearchState) -> ResearchState:
        """获取模板公司详细信息节点"""
        company_info = state["company_info"]
        name = company_info.get("name", "the template company")
        website = company_info.get("website", "")

        dispatch_custom_event(
            "thinking",
            f"I will first get the detailed information of the template company {name} to understand its business model, products, and technical requirements.",  # noqa: E501
        )

        # 构建工具调用参数
        tool_input = {}

        if website:
            tool_input["domains"] = [website]

        # 获取公司详细信息
        company_details = await get_company_details.ainvoke(tool_input)
        detail = str(company_details)

        # 把 company_info 与 company_details 一起 整合成 yaml 字符串
        company_details = yaml.dump(
            {
                **company_info,
                "detail": detail,
            },
            default_flow_style=False,
            sort_keys=False,
        )

        state["template_company_with_detail"] = company_details

        return state

    async def _search_similar_companies_node(self, state: ResearchState) -> ResearchState:
        """根据模板公司详细信息搜索相似公司节点"""
        company_name = state["company_info"].get("name", "the template company")
        dispatch_custom_event(
            "thinking",
            f"I will search for some companies similar to {company_name} and research them. Next, I will gradually achieve this goal.",  # noqa: E501
        )

        template_company_with_detail = state.get("template_company_with_detail")
        if template_company_with_detail is None:
            raise KeyError("template_company_with_detail is missing in state")

        similar_companies: SimilarCompaniesResult = await search_similar_companies(
            user_query=state["user_query"],
            template_company_with_detail=template_company_with_detail,
            excludes=state.get("excludes"),
        )
        state["title"] = similar_companies.title
        state["similar_companies"] = similar_companies.companies

        company_names = ", ".join([company.name for company in similar_companies.companies])
        dispatch_custom_event(
            "thinking", f"I have searched for {len(state['similar_companies'])} similar companies: {company_names}."
        )
        return state

    async def _research_companies_node(self, state: ResearchState) -> ResearchState:
        """深度调研候选公司节点"""
        similar_companies = state["similar_companies"]
        if len(similar_companies) == 0:
            raise ValueError("similar_companies is empty")

        logger.info(f"开始深度调研候选公司，共 {len(similar_companies)} 家公司")

        company_names = ", ".join([company.name for company in similar_companies])
        dispatch_custom_event(
            "thinking",
            f"Next, I will conduct in-depth research on these {len(similar_companies)} companies: {company_names}",
        )

        # 并行调研所有候选公司
        tasks = [
            research_company(company_basic_info=company, user_query=state["user_query"])
            for company in similar_companies
        ]
        research_results = await asyncio.gather(*tasks)

        # 过滤掉错误结果，返回调研结果
        research_results = [result.get("research_result") for result in research_results if "error" not in result]
        if len(research_results) == 0:
            raise Exception("research results is empty")

        logger.info(f"深度调研候选公司完成，共获得 {len(research_results)} 家公司的调研结果")
        state["research_results"] = research_results

        dispatch_custom_event(
            "thinking", f"Okay, I have completed the in-depth research on {len(research_results)} candidate companies."
        )
        return state

    async def _extract_results_node(self, state: ResearchState) -> ResearchState:
        """提取调研结果节点"""
        dispatch_custom_event(
            "thinking", "Next, I will extract information from the research results of candidate companies."
        )

        extract_result: ExtractResearchResult = await extract_research_results(
            research_results=state["research_results"]
        )
        state["extract_result"] = extract_result

        dispatch_custom_event(
            "thinking",
            "Now, I have got the final result, and I will show it to you soon.",
        )
        return state

    async def execute(self, initial_state: ResearchState) -> dict:
        """执行调研工作流"""
        logger.info("开始执行调研工作流...")
        try:
            final_state: ResearchState = await self.graph.ainvoke(initial_state)
            logger.info("调研工作流执行完成")

            # 校验返回结果
            if not final_state["extract_result"]:
                raise Exception("No valid research result")

            extract_result: ExtractResearchResult = final_state["extract_result"]
            result_data = extract_result.model_dump().get("data", [])

            return {
                "title": final_state["title"],
                "data": result_data,
            }
        except Exception as e:
            logger.error(f"调研工作流执行失败: {str(e)}")
            raise Exception(f"execute research workflow failed: {str(e)}")


async def execute_research_workflow(
    user_query: str,
    company_info: dict,
    excludes: list[str],
    callbacks: Optional[list[BaseCallbackHandler]] = None,
) -> dict:
    """
    执行调研工作流

    Args:
        user_query: user query, customer prompt
        company_info: CompanyInfo
        excludes: list of company names to exclude
        callbacks: list of callback handlers, used to handle the event stream

    Returns:
        dict: 调研结果
        {
            "title": "...", # 标题
            "data": [] # 调研结果
        }
    """
    name = company_info.get("name", "")
    website = company_info.get("website", "")
    if not name and not website:
        raise Exception("Missing company name or website")

    # 排除当前公司
    if len(excludes) == 0 or name not in excludes:
        excludes.append(name)

    logger.info(
        f"开始执行调研任务, company_info: {company_info}, user_query: {user_query}, excludes: {excludes}"  # noqa: E501
    )

    initial_state = ResearchState(
        user_query=user_query,
        company_info=company_info,
        excludes=excludes,
    )
    research_state_graph = ResearchStateGraph(callbacks=callbacks)
    return await research_state_graph.execute(initial_state)


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()
    # 运行示例

    async def main():
        user_query = "I want to know companies like iiNet rand who use edge gateways for its networking"  # noqa: E501
        company_info = {
            "name": "iiNet",
            "website": "http://www.iinet.net.au/",
            "description": "iiNet is an industry-leading Australian internet provider with services available nationwide. With our wide range of NBN plans and our own ULTRA Broadband Cable, FTTB & VDSL2 networks, we make it easy for Aussies to connect to great-value broadband.",  # noqa: E501
            "territory": "Australia",
            "industry": "ICT",
            "market_segments": ["DA"],
        }

        excludes = ["Cirrus Link Solutions", "Ingersoll Rand", "Inhand Networks"]

        result = await execute_research_workflow(user_query, company_info, excludes)
        logger.info(json.dumps(result, ensure_ascii=False, indent=4))

    asyncio.run(main())
