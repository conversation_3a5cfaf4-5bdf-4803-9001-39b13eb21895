import asyncio
import json
import os
from pathlib import Path

import httpx
import yaml
from diskcache import Cache
from langchain_core.tools import tool
from loguru import logger

# 创建缓存目录和Cache对象
CACHE_DIR = Path(__file__).parent / ".cache"
CACHE_DIR.mkdir(parents=True, exist_ok=True)

# 为Apollo API创建缓存
apollo_cache = Cache(
    directory=str(CACHE_DIR / "apollo"),
    eviction_policy="least-recently-used",
    size_limit=int(1e9),
    cull_limit=10,
)

# 设置默认缓存期限为30天（以秒为单位）
CACHE_EXPIRY = 30 * 24 * 60 * 60  # 30天


class ApolloAPIClient:
    """
    Apollo API客户端类，封装所有API调用功能
    """

    def __init__(self):
        self._client: httpx.AsyncClient | None = None
        self._semaphore = asyncio.Semaphore(2)

    @property
    def client(self) -> httpx.AsyncClient:
        """
        懒加载创建HTTP客户端
        """
        if self._client is None:
            # check required environment variables
            apollo_api_key = os.environ.get("APOLLO_API_KEY")

            if not apollo_api_key:
                raise ValueError("APOLLO_API_KEY environment variable is required")

            self._client = httpx.AsyncClient(
                timeout=30.0,
                headers={"X-Api-Key": apollo_api_key},
                base_url="https://api.apollo.io/api/v1",
                proxy=os.environ.get("OPENAI_PROXY", None),
            )
            logger.info("Apollo API client initialized")
        return self._client

    async def search_organization(self, **kwargs) -> dict:
        """
        搜索企业信息
        Args:
            **kwargs: 搜索参数
        Returns:
            包含企业信息的字典或错误信息
        """
        # add caching logic
        cache_key = f"org_search_{hash(str(sorted(kwargs.items())))}"
        result = apollo_cache.get(cache_key)

        if result is not None:
            return result

        async with self._semaphore:
            logger.debug(f"send request to search organizations with params: {kwargs}")

            data = {
                **kwargs,
                "per_page": 100,
            }

            response = await self.client.post("/mixed_companies/search", json=data)

            if response.status_code == 200:
                logger.debug("successfully fetch organization search results")
                result = response.json()
                organizations = result.get("organizations", [])
                organizations = self.simplify_organization_results(organizations)
                # cache successful results
                apollo_cache.set(cache_key, organizations, expire=CACHE_EXPIRY)
                return organizations
            else:
                error_msg = f"Error: {response.status_code}, {response.text}"
                logger.warning(f"failed to search organizations: {error_msg}")
                return {"error": error_msg}

    async def get_organization_by_id(self, organization_id: str) -> dict:
        """
        通过组织 ID 获取详细的组织信息
        Args:
            organization_id: 组织的唯一标识符
        Returns:
            包含组织详细信息的字典或错误信息
        """
        # add caching logic
        cache_key = f"org_detail_{organization_id}"
        result = apollo_cache.get(cache_key)

        if result is not None:
            return result

        async with self._semaphore:
            logger.debug(f"send request to get organization by id: {organization_id}")

            response = await self.client.get(f"/organizations/{organization_id}")

            if response.status_code == 200:
                logger.debug(f"successfully fetch organization details for id: {organization_id}")
                result = response.json()
                organization = result.get("organization", {})
                # cache successful results
                apollo_cache.set(cache_key, organization, expire=CACHE_EXPIRY)
                return organization
            else:
                error_msg = f"Error: {response.status_code}, {response.text}"
                logger.warning(f"failed to get organization by id {organization_id}: {error_msg}")
                return {"error": error_msg}

    async def search_people(
        self,
        organization_ids: list[str],
        person_titles: list[str] = [],
        seniorities: list[str] = [],
        locations: list[str] = [],
        page: int = 1,
    ) -> dict:
        """
        搜索人员信息
        Args:
            organization_ids: 公司ID列表
            person_titles: 职位标题列表
            seniorities: 级别列表
            locations: 位置列表
            page: 页码
        Returns:
            包含人员信息的字典或错误信息
        """
        async with self._semaphore:
            logger.debug(f"send request to search people with org_ids: {organization_ids}")

            # 构建请求体
            data = {
                "page": page,
                "per_page": 100,
                "organization_ids": organization_ids,
            }

            # 添加可选参数
            if person_titles:
                data["person_titles"] = person_titles

            if seniorities:
                data["person_seniorities"] = seniorities

            if locations:
                data["person_locations"] = locations

            response = await self.client.post("/mixed_people/search", json=data)

            if response.status_code == 200:
                logger.debug("successfully fetch people search results")
                result = response.json()
                people = result.get("people", [])
                if people:
                    people = self.simplify_people_results(people)
                    result["people"] = people

                return result
            else:
                error_msg = f"Apollo API请求失败 - 状态码: {response.status_code}, 消息: {response.text}"
                logger.warning(f"failed to search people: {error_msg}")
                return {"error": error_msg}

    @staticmethod
    def simplify_organization_results(organizations: list) -> list:
        """
        简化企业搜索结果
        """
        return [
            {
                "name": item.get("name", ""),
                "id": item["id"],
                "website_url": item.get("website_url", ""),
                "linkedin_url": item.get("linkedin_url", ""),
                "founded_year": item.get("founded_year", ""),
                "organization_revenue": item.get("organization_revenue_printed", ""),
                "phone": item.get("phone", None),
            }
            for item in organizations
        ]

    @staticmethod
    def simplify_people_results(people: list) -> list:
        """
        简化人员搜索结果
        """
        simplified_results = []
        for person in people:
            simplified_person = {
                "name": f"{person.get('first_name', '')} {person.get('last_name', '')}",
                "title": person.get("title", ""),
                "company": person.get("organization", {}).get("name", ""),
                "location": person.get("location", {}).get("city", "") or person.get("city", ""),
                "country": person.get("location", {}).get("country", "") or person.get("country", ""),
                "linkedin_url": person.get("linkedin_url", ""),
                "seniority": person.get("seniority", ""),
                "department": person.get("department", ""),
            }
            simplified_results.append(simplified_person)
        return simplified_results

    async def close(self):
        """
        关闭HTTP客户端连接
        """
        if self._client is not None:
            await self._client.aclose()
            self._client = None


# 全局API客户端实例
apollo = ApolloAPIClient()


@tool(parse_docstring=True)
async def search_organizations(
    q_organization_name: str | None = None,
    q_organization_keyword_tags: list[str] | None = None,
    organization_locations: list[str] | None = None,
    organization_num_employees_ranges: list[str] | None = None,
) -> str:
    """
    Use the Organization Search endpoint to find companies in the Apollo database. Several filters are available to help narrow your search.
    This method only provides company name, company website, company headquarters address, and LinkedIn company homepage URL.
    All of the params must be in English.

    Args:
        q_organization_name: Filter search results to include a specific company name.
        q_organization_keyword_tags: Filter search results based on keywords associated with companies. For example, you can enter mining as a value to return only companies that have an association with the mining industry.
        organization_locations: The location of the company headquarters. You can search across cities, US states, and countries.
        organization_num_employees_ranges:
            The number range of employees working for the company. This enables you to find companies based on headcount. You can add multiple ranges to expand your search results.
            Each range you add needs to be a string, with the upper and lower numbers of the range separated only by a comma.
            Examples 1,10; 250,500; 10000,20000

    Returns:
        Search results in YAML format
    """  # noqa: E501
    logger.info(f"搜索目标企业: {q_organization_name}")

    try:
        result = await apollo.search_organization(
            q_organization_name=q_organization_name,
            q_organization_keyword_tags=q_organization_keyword_tags,
            organization_locations=organization_locations,
            organization_num_employees_ranges=organization_num_employees_ranges,
        )
    except Exception as e:
        logger.error(f"搜索企业时出错: {str(e)}")
        return json.dumps({"error": str(e)})

    if "error" in result:
        return result["error"]
    else:
        logger.info(f"共找到 {len(result)} 个目标企业")
        # filter fields in result list
        return f"```yaml\n{yaml.dump(result, indent=2)}\n```"


@tool(parse_docstring=True)
async def get_organization_by_id(
    organization_id: str,
) -> str:
    """
    Get detailed information about a specific organization by its Apollo ID.
    This method provides comprehensive organization details including company information, contacts, and more.

    Args:
        organization_id: The unique Apollo ID of the organization to retrieve.

    Returns:
        Organization details in YAML format
    """
    logger.info(f"获取组织详细信息，ID: {organization_id}")

    try:
        result = await apollo.get_organization_by_id(organization_id)
    except Exception as e:
        logger.error(f"获取组织信息时出错: {str(e)}")
        return json.dumps({"error": str(e)})

    if "error" in result:
        return result["error"]
    else:
        logger.info(f"成功获取组织信息: {result.get('name', 'Unknown')}")
        return f"```yaml\n{yaml.dump(result, indent=2)}\n```"


@tool(parse_docstring=True)
async def search_people(
    organization_ids: list[str],
    person_titles: list[str] | None = None,
    seniorities: list[str] | None = None,
    locations: list[str] | None = None,
    page: int = 1,
) -> str:
    """
    使用Apollo API搜索人员信息，支持分页功能。当结果数量超过单页限制时，可以通过调整page参数获取更多结果。
    如果第一页结果不足，可以使用page=2,3...等获取后续页面的数据。每次调用返回指定limit数量的结果。
    All the params must be in English.

    Args:
        organization_ids: List of company IDs to search for. For example, ["1234567890", "1234567891"]
        person_titles: Job titles held by the people you want to find. For a person to be included in search results,
            they only need to match 1 of the job titles you add. Adding more job titles expands your search results.
            Results also include job titles with the same terms, even if they are not exact matches.
            Must be in English.
            For example, searching for marketing manager might return people with the job title content marketing manager.
        seniorities: Job levels that you want to include in the search results.
            Only include job levels that are relevant to your search.
            For example, searching for VP might return people with the job title vice president.
            The following options can be used for this parameter,
            owner, founder, c_suite, partner, vp, head, director, manager, senior, entry, intern
        locations: The location where people live. You can search across cities, US states, and countries.
        page: The page number of the Apollo data that you want to retrieve.

    Returns:
        A JSON string containing the search results
    """  # noqa: E501
    logger.info("使用Apollo API搜索人员")

    try:
        result = await apollo.search_people(
            organization_ids=organization_ids,
            person_titles=person_titles,
            seniorities=seniorities,
            locations=locations,
            page=page,
        )

        if "error" in result:
            return json.dumps({"error": result["error"]})

        people = result.get("people", [])
        page_info = result.get("pagination", {})
        total_entries = page_info.get("total_entries", 0)

        if total_entries > 200:
            return f"返回结果{total_entries}条，超过200条，请调整搜索条件或分页参数。"

        logger.info(
            f"通过Apollo API搜索到 {len(people)} 个结果, 第 {page} 页, 共 {page_info.get('total_pages', 0)} 页"  # noqa: E501
        )

        yaml_str = yaml.dump(
            {
                "pagination": page_info,
                "people": people,
            },
            indent=2,
        )
        return f"""
```yaml
{yaml_str}
```
{f"总人数: {total_entries}, 数量太少，建议请调整搜索条件或分页参数。" if total_entries < 20 else ""}
"""

    except Exception as e:
        error_msg = f"使用Apollo API搜索人员时出错: {str(e)}"
        logger.error(error_msg)
        return json.dumps({"error": error_msg})


if __name__ == "__main__":
    import dotenv

    dotenv.load_dotenv()

    async def main():
        # test organization search
        org_result = await apollo.search_organization(
            q_organization_name="Apple", organization_locations=["United States"]
        )
        print("Organization Search Results:")
        print(yaml.dump(org_result, indent=2))

        # test people search
        if org_result and len(org_result) > 0 and "error" not in org_result:
            people_result = await apollo.search_people(
                organization_ids=[org_result[0]["id"]], person_titles=["CEO", "CTO"], page=1
            )
            print("\nPeople Search Results:")
            print(yaml.dump(people_result, indent=2))

    asyncio.run(main())
