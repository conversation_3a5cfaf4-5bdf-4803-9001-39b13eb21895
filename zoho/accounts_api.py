from typing import NotRequired, Optional, TypedDict

import httpx

from config import (
    MAX_KEEPALIVE_CONNECTIONS,
    ZOHO_API_HOST,
    ZOHO_API_KEY,
)
from utils.logger import get_logger

logging = get_logger(__name__)


class ZohoAccountInfo(TypedDict, total=False):
    """zoho account info structure"""

    id: str
    owner: NotRequired[dict]  # contains name, id, email
    modified_by: NotRequired[dict]  # contains name, id, email
    created_time: NotRequired[str]
    created_by: NotRequired[dict]  # contains name, id, email
    modified_time: NotRequired[str]
    description: NotRequired[str]
    state: NotRequired[dict]  # contains sharing_permission, state, approval, review_process, etc.
    billing_address: NotRequired[dict]  # contains state, region, validate
    shipping_address: NotRequired[dict]  # contains validate
    currency_symbol: NotRequired[str]
    tag: NotRequired[list]
    name: str  # account name
    secondary_industry: NotRequired[list]
    territories: NotRequired[list]  # contains objects with name and id
    territory: NotRequired[str]
    last_activity_time: NotRequired[str]
    industry: NotRequired[str]
    stage: NotRequired[str]
    priority: NotRequired[str]
    market_segments: NotRequired[list]
    website: NotRequired[str]
    lead_source: NotRequired[str]
    billing_state: NotRequired[str]


class ZohoUserInfo(TypedDict, total=False):
    """zoho user info structure"""

    id: str  # required field
    country: NotRequired[str]
    role: NotRequired[dict]  # contains name, id
    profile: NotRequired[dict]  # contains name, id
    locale: NotRequired[str]
    first_name: NotRequired[str]
    last_name: NotRequired[str]
    email: NotRequired[str]
    created_by: NotRequired[dict]  # contains name, id
    zuid: NotRequired[str]
    enabled: NotRequired[bool]
    status: NotRequired[str]


zoho_client = httpx.AsyncClient(
    base_url=ZOHO_API_HOST,
    timeout=30.0,
    headers={"x-api-key": ZOHO_API_KEY},
    limits=httpx.Limits(max_keepalive_connections=MAX_KEEPALIVE_CONNECTIONS),
)


async def fetch_accounts_info_by_account_id(account_id: str) -> Optional[ZohoAccountInfo]:
    """根据 account_id 获取账户信息"""
    try:
        response = await zoho_client.get(f"/api/crm/zoho/module/accounts/{account_id}")
        if response.status_code == 200:
            return response.json().get("result", {})
        else:
            logging.error(f"获取账户信息失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        logging.error(f"获取账户信息失败: {str(e)}")

    return None


async def get_owner_info_by_user_id(user_id: str) -> Optional[ZohoUserInfo]:
    """根据 user_id 获取用户信息"""
    try:
        response = await zoho_client.get(f"/api/crm/zoho/users/{user_id}")
        if response.status_code == 200:
            return response.json().get("result", {})
        else:
            logging.error(f"获取用户信息失败: {response.status_code} - {response.text}")
    except Exception as e:
        logging.error(f"获取用户信息失败: {str(e)}")

    return None


async def add_account_to_zoho(account_info: dict) -> ZohoAccountInfo:
    """调用 zoho-api 添加账户"""
    try:
        response = await zoho_client.post("/api/crm/zoho/module/account/create", json=account_info)

        if response.status_code != 200:
            logging.error(f"调用 zoho-api 添加账户失败 - 状态码: {response.status_code}, 响应: {response.text}")
            raise Exception(
                f"add account to zoho failed - status code: {response.status_code}, response: {response.text}"
            )
        else:
            result = response.json().get("result", {})
            if "id" not in result:
                error = response.json().get("error", {})
                raise Exception(f"{error}")

            logging.info(f"调用 zoho-api 添加 Account 成功: {result['id']}")
            return result
    except Exception as e:
        logging.error(f"调用 zoho-api 添加账户发生异常: {e}")
        raise Exception(f"Add account to zoho failed, {e}")


async def main():
    account_id = "3091799000289213015"
    account_info = await fetch_accounts_info_by_account_id(account_id)
    logging.info(f"account_info: {account_info}")

    print(await get_owner_info_by_user_id("3091799000035539002"))


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
