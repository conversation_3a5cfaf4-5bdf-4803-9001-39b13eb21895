from typing import Optional

import httpx

from config import (
    MAX_KEEPALIVE_CONNECTIONS,
    ZOHO_API_HOST,
    ZOHO_API_KEY,
)
from utils.logger import get_logger

logging = get_logger(__name__)


zoho_client = httpx.Client(
    base_url=ZOHO_API_HOST,
    timeout=30.0,
    headers={"x-api-key": ZOHO_API_KEY},
    limits=httpx.Limits(max_keepalive_connections=MAX_KEEPALIVE_CONNECTIONS),
)


def add_contacts_to_zoho(account_id: str, contacts: list[dict], owner_id: Optional[str] = None):
    """
    调用 zoho-api 添加联系人

    Args:
        account_id: 当前发起探查的公司 id
        contacts: 联系人列表
        owner_id: 所有者 id
    """
    if not account_id or not contacts or len(contacts) == 0:
        return

    try:
        payload = {
            "account_id": account_id,
            "data": {
                "total": len(contacts),
                "type": "data_transform",
                "data": contacts,
            },
            "owner_id": owner_id,  # 这里的 owner_id 是示例，请替换为实际的 owner_id
        }
        response = zoho_client.post("/api/plm/common/contacts/flow", json=payload)

        if response.status_code != 200:
            logging.error(f"调用 zoho-api 添加联系人失败 - 状态码: {response.status_code}, 响应: {response.text}")
            raise Exception(
                f"add contacts to zoho failed - status code: {response.status_code}, response: {response.text}"
            )

    except Exception as e:
        logging.error(f"调用 zoho-api 添加联系人发生异常: {e}")
        raise e


def get_contact_by_id(contact_id: str) -> dict | None:
    """调用 zoho-api 获取联系人信息"""
    try:
        response = zoho_client.get(f"/api/crm/zoho/module/contacts/{contact_id}")

        if response.status_code == 200:
            results = response.json()
            return results.get("result", None)
        else:
            logging.error(f"调用 zoho-api 获取联系人信息失败 - 状态码: {response.status_code}, 响应: {response.text}")
    except Exception as e:
        logging.error(f"调用 zoho-api 获取联系人信息发生异常: {e}")

    return None


def update_contact_by_id(contact_id: str, contact_info: dict):
    """调用 zoho-api 更新联系人信息"""
    response = zoho_client.put(f"/api/crm/zoho/module/contacts/{contact_id}", json=contact_info)
    if response.status_code == 200:
        return  # ignore response
    else:
        raise Exception(f"调用 zoho-api 更新联系人信息失败 - 状态码: {response.status_code}, 响应: {response.text}")


# 测试 unassign contact readd to account
def unassign_contact_and_readd_to_account():
    """
    测试 unassign contact readd to account
    仅供测试使用
    """
    account_id = "3091799000301445003"
    owner_id = "*********"
    contacts = [
        {
            "First_Name": "Bahar",
            "Last_Name": "Faridani",
            "Email": "<EMAIL>",
            "Phone": "",
            "Title": "Public Relations Coordinator | Founder",
            "Department": "C-Suite, Marketing",
            "Mobile": "",
            "Function_Type": "Business Senior Management",
            "Secondary_Email": "",
            "LinkedIn": "http://www.linkedin.com/in/bahar-faridani-8b54ba260",
            "Currency": "USD",
            "Labels": "Sales Agent",
            "Associated_Account_Type": "EU(End User)",
            "Associated_Industry": "ICT",
            "Lead_Source": "LinkedIn",
            "Twitter": "",
            "Facebook": "",
            "Region": "APAC",
            "Rec_Reason": "Bahar Faridani is the founder and public relations coordinator of SpinTel in Australia, holding ultimate decision-making authority, especially for technology and business-related solutions. Her leadership role and deep engagement with company operations make her a primary decision maker for introducing new industrial IoT and communication solutions.",  # noqa: E501
            "Level": "highest",
            "Notes": {
                "Title": "Reason For Recommendation",
                "Content": "Bahar Faridani is the founder and public relations coordinator of SpinTel in Australia, holding ultimate decision-making authority, especially for technology and business-related solutions. Her leadership role and deep engagement with company operations make her a primary decision maker for introducing new industrial IoT and communication solutions.",  # noqa: E501
            },
        }
    ]
    logging.info(f"unassign contact and readd to account: {account_id}")
    logging.info(f"contacts: {contacts}")
    logging.info(f"owner_id: {owner_id}")
    add_contacts_to_zoho(account_id=account_id, contacts=contacts, owner_id=owner_id)


# 使用示例
if __name__ == "__main__":
    unassign_contact_and_readd_to_account()
    # import json

    # contact_id = "3091799000299653001"
    # contact_info = get_contact_by_id(contact_id)
    # print(json.dumps(contact_info, indent=4))
    # update_contact_by_id(contact_id, {"Phone": "***********", "Mobile": "***********"})
